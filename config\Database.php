<?php
// config/Database.php
namespace Config;

use PDO;
use PDOException;

class Database {
    private static ?PDO $connection = null;

    public static function getConnection(): PDO {
        if (self::$connection === null) {
            $host = 'localhost'; // Your MySQL host
            $db   = 'police_portal_3'; // Your database name
            $user = 'root'; // Your database user
            $pass = ''; // Your database password
            $charset = 'utf8mb4';

            $dsn = "mysql:host=$host;dbname=$db;charset=$charset";
            $options = [
                PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES   => false,
            ];

            try {
                self::$connection = new PDO($dsn, $user, $pass, $options);
            } catch (PDOException $e) {
                // Log error and terminate or handle gracefully
                error_log('Database connection error: ' . $e->getMessage());
                die('Database connection failed.'); // Or throw custom exception
            }
        }
        return self::$connection;
    }
}