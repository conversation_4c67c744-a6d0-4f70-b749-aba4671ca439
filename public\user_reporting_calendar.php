<?php
// Police Portal Pro: Hierarchical Reporting System
// Secure 4-level station hierarchy reporting with versioned edits and aggregated roll-ups
declare(strict_types=1);

require_once __DIR__ . '/_auth_user.php';
require_once __DIR__ . '/../config/db.php';

// Compatibility layer for authentication transition
if (!isset($_SESSION['username']) && isset($_SESSION['badge_id'])) {
    // Map badge_id to username for backward compatibility
    $badgeToUserMap = [
        'A1001' => 'admin',
        'B2002' => 'regmanager',
        'C3003' => 'johndoe'
    ];
    $_SESSION['username'] = $badgeToUserMap[$_SESSION['badge_id']] ?? 'admin';
}

// Initialize database tables for reporting system (using new office schema)
function initializeReportingTables(PDO $pdo): void {
    try {
        // Check if new schema tables exist
        $stmt = $pdo->query("SHOW TABLES LIKE 'office_report_templates'");
        if ($stmt->rowCount() == 0) {
            throw new Exception("New schema tables not found. Please run migration first.");
        }

        // Check if default template exists, if not create it
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM office_report_templates WHERE is_active = TRUE");
        $stmt->execute();
        $templateCount = $stmt->fetchColumn();

        if ($templateCount == 0) {
            // Insert default report template for new schema
            $defaultTemplate = [
                'name' => 'Daily Operations Report',
                'description' => 'Standard daily numerical reporting for all offices',
                'fields' => json_encode([
                    'incidents_reported' => ['type' => 'number', 'label' => 'Incidents Reported', 'required' => true],
                    'tasks_completed' => ['type' => 'number', 'label' => 'Tasks Completed', 'required' => true],
                    'calls_handled' => ['type' => 'number', 'label' => 'Calls Handled', 'required' => true],
                    'work_hours' => ['type' => 'number', 'label' => 'Work Hours', 'required' => true, 'step' => '0.1'],
                    'client_contacts' => ['type' => 'number', 'label' => 'Client Contacts', 'required' => true],
                    'documents_processed' => ['type' => 'number', 'label' => 'Documents Processed', 'required' => true],
                    'meetings_attended' => ['type' => 'number', 'label' => 'Meetings Attended', 'required' => true],
                    'issues_resolved' => ['type' => 'number', 'label' => 'Issues Resolved', 'required' => true]
                ]),
                'office_level' => 1
            ];

            $stmt = $pdo->prepare("
                INSERT INTO office_report_templates (name, description, fields, office_level)
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([
                $defaultTemplate['name'],
                $defaultTemplate['description'],
                $defaultTemplate['fields'],
                $defaultTemplate['office_level']
            ]);
        }
    } catch (Exception $e) {
        // If tables don't exist, create them
        createNewSchemaTables($pdo);
    }
}

// Create new schema tables if they don't exist
function createNewSchemaTables(PDO $pdo): void {
    // Disable foreign key checks temporarily
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");

    // Create offices table first (no dependencies)
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS offices (
            office_id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            level INT NOT NULL,
            location VARCHAR(255) NOT NULL,
            phone_number VARCHAR(20),
            permissions JSON,
            parent_office_id INT,
            UNIQUE (name, level),
            INDEX idx_parent_office (parent_office_id),
            INDEX idx_level (level)
        ) ENGINE=InnoDB
    ");

    // Create users table (depends on offices)
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            user_id INT PRIMARY KEY AUTO_INCREMENT,
            full_name VARCHAR(100) NOT NULL,
            username VARCHAR(50) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            permissions JSON,
            office_id INT NOT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_office_id (office_id),
            INDEX idx_username (username)
        ) ENGINE=InnoDB
    ");

    // Create history table (depends on users)
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS history (
            history_id INT PRIMARY KEY AUTO_INCREMENT,
            entity_type ENUM('office', 'user') NOT NULL,
            entity_id INT NOT NULL,
            change_type VARCHAR(50) NOT NULL,
            old_value JSON,
            new_value JSON,
            changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            changed_by INT,
            INDEX idx_entity (entity_type, entity_id),
            INDEX idx_changed_at (changed_at)
        ) ENGINE=InnoDB
    ");

    // Create office daily reports table (depends on offices and users)
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS office_daily_reports (
            id INT AUTO_INCREMENT PRIMARY KEY,
            report_date DATE NOT NULL,
            office_id INT NOT NULL,
            user_id INT NOT NULL,
            report_data JSON NOT NULL,
            version INT NOT NULL DEFAULT 1,
            is_current BOOLEAN NOT NULL DEFAULT TRUE,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            created_by INT NOT NULL,
            edited_by INT NULL,
            edited_at DATETIME NULL,
            INDEX idx_date_office (report_date, office_id),
            INDEX idx_current (is_current),
            INDEX idx_office_id (office_id),
            INDEX idx_user_id (user_id),
            INDEX idx_created_by (created_by),
            INDEX idx_edited_by (edited_by)
        ) ENGINE=InnoDB
    ");

    // Create office aggregated reports table (depends on offices)
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS office_aggregated_reports (
            id INT AUTO_INCREMENT PRIMARY KEY,
            report_date DATE NOT NULL,
            office_id INT NOT NULL,
            aggregated_data JSON NOT NULL,
            last_updated DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_date_office (report_date, office_id),
            INDEX idx_date (report_date),
            INDEX idx_office_id (office_id)
        ) ENGINE=InnoDB
    ");

    // Create office report templates table (no dependencies)
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS office_report_templates (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            fields JSON NOT NULL,
            office_level INT NOT NULL,
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_level_active (office_level, is_active)
        ) ENGINE=InnoDB
    ");

    // Insert sample data first
    insertSampleData($pdo);

    // Now add foreign key constraints after data is inserted
    addForeignKeyConstraints($pdo);

    // Re-enable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
}

// Add foreign key constraints after tables and data are created
function addForeignKeyConstraints(PDO $pdo): void {
    try {
        // Add foreign key for offices self-reference
        $pdo->exec("
            ALTER TABLE offices
            ADD CONSTRAINT fk_offices_parent
            FOREIGN KEY (parent_office_id) REFERENCES offices(office_id) ON DELETE RESTRICT
        ");
    } catch (Exception $e) {
        // Constraint might already exist, ignore
    }

    try {
        // Add foreign key for users -> offices
        $pdo->exec("
            ALTER TABLE users
            ADD CONSTRAINT fk_users_office
            FOREIGN KEY (office_id) REFERENCES offices(office_id) ON DELETE RESTRICT
        ");
    } catch (Exception $e) {
        // Constraint might already exist, ignore
    }

    try {
        // Add foreign key for history -> users
        $pdo->exec("
            ALTER TABLE history
            ADD CONSTRAINT fk_history_user
            FOREIGN KEY (changed_by) REFERENCES users(user_id) ON DELETE SET NULL
        ");
    } catch (Exception $e) {
        // Constraint might already exist, ignore
    }

    try {
        // Add foreign keys for office_daily_reports
        $pdo->exec("
            ALTER TABLE office_daily_reports
            ADD CONSTRAINT fk_reports_office
            FOREIGN KEY (office_id) REFERENCES offices(office_id) ON DELETE CASCADE
        ");

        $pdo->exec("
            ALTER TABLE office_daily_reports
            ADD CONSTRAINT fk_reports_user
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        ");

        $pdo->exec("
            ALTER TABLE office_daily_reports
            ADD CONSTRAINT fk_reports_created_by
            FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE CASCADE
        ");

        $pdo->exec("
            ALTER TABLE office_daily_reports
            ADD CONSTRAINT fk_reports_edited_by
            FOREIGN KEY (edited_by) REFERENCES users(user_id) ON DELETE CASCADE
        ");
    } catch (Exception $e) {
        // Constraints might already exist, ignore
    }

    try {
        // Add foreign key for office_aggregated_reports
        $pdo->exec("
            ALTER TABLE office_aggregated_reports
            ADD CONSTRAINT fk_aggregated_office
            FOREIGN KEY (office_id) REFERENCES offices(office_id) ON DELETE CASCADE
        ");
    } catch (Exception $e) {
        // Constraint might already exist, ignore
    }
}

// Insert sample data for new schema
function insertSampleData(PDO $pdo): void {
    // Check if data already exists
    $stmt = $pdo->query("SELECT COUNT(*) FROM offices");
    if ($stmt->fetchColumn() > 0) {
        return; // Data already exists
    }

    // Insert sample offices (without explicit IDs, let auto-increment work)
    $offices = [
        ['Head Office', 4, 'HQ, New York', '555-0100', '{"admin_access": true, "view_all_reports": true, "edit_all_reports": true}', null],
        ['Region A', 3, 'Chicago', '555-0101', '{"regional_access": true, "view_region_reports": true, "edit_region_reports": true}', 1],
        ['Region B', 3, 'Los Angeles', '555-0105', '{"regional_access": true, "view_region_reports": true, "edit_region_reports": true}', 1],
        ['Branch A1', 2, 'Chicago North', '555-0102', '{"branch_access": true, "view_branch_reports": true, "edit_branch_reports": true}', 2],
        ['Branch A2', 2, 'Chicago South', '555-0106', '{"branch_access": true, "view_branch_reports": true, "edit_branch_reports": true}', 2],
        ['Branch B1', 2, 'LA Downtown', '555-0107', '{"branch_access": true, "view_branch_reports": true, "edit_branch_reports": true}', 3],
        ['Office A1-1', 1, 'Downtown Chicago', '555-0103', '{"local_access": true, "view_local_reports": true, "edit_local_reports": true}', 4],
        ['Office A1-2', 1, 'Uptown Chicago', '555-0104', '{"local_access": true, "view_local_reports": true, "edit_local_reports": true}', 4],
        ['Office A2-1', 1, 'South Side Chicago', '555-0108', '{"local_access": true, "view_local_reports": true, "edit_local_reports": true}', 5],
        ['Office B1-1', 1, 'LA Central', '555-0109', '{"local_access": true, "view_local_reports": true, "edit_local_reports": true}', 6]
    ];

    // Insert Head Office first (no parent)
    $stmt = $pdo->prepare("
        INSERT INTO offices (name, level, location, phone_number, permissions, parent_office_id)
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    $stmt->execute($offices[0]);
    $headOfficeId = $pdo->lastInsertId();

    // Insert Regions (parent = Head Office)
    $regionIds = [];
    for ($i = 1; $i <= 2; $i++) {
        $office = $offices[$i];
        $office[5] = $headOfficeId; // Set parent to Head Office
        $stmt->execute($office);
        $regionIds[] = $pdo->lastInsertId();
    }

    // Insert Branches (parent = Regions)
    $branchIds = [];
    for ($i = 3; $i <= 5; $i++) {
        $office = $offices[$i];
        if ($i <= 4) {
            $office[5] = $regionIds[0]; // Region A
        } else {
            $office[5] = $regionIds[1]; // Region B
        }
        $stmt->execute($office);
        $branchIds[] = $pdo->lastInsertId();
    }

    // Insert Local Offices (parent = Branches)
    for ($i = 6; $i <= 9; $i++) {
        $office = $offices[$i];
        if ($i <= 7) {
            $office[5] = $branchIds[0]; // Branch A1
        } else if ($i == 8) {
            $office[5] = $branchIds[1]; // Branch A2
        } else {
            $office[5] = $branchIds[2]; // Branch B1
        }
        $stmt->execute($office);
    }

    // Insert sample users (without explicit IDs)
    $users = [
        ['Admin User', 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '{"admin_access": true, "approve": true, "view_all": true}', $headOfficeId],
        ['Regional Manager', 'regmanager', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '{"regional_access": true, "approve": true}', $regionIds[0]],
        ['Branch Supervisor', 'supervisor', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '{"branch_access": true, "approve": false}', $branchIds[2]],
        ['Jane Smith', 'janesmith', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '{"read_write": true, "approve": true}', $branchIds[0]],
        ['John Doe', 'johndoe', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '{"read_write": true, "approve": false}', 7] // Will be updated to correct office ID
    ];

    // Get the last office ID for John Doe
    $stmt = $pdo->query("SELECT office_id FROM offices WHERE name = 'Office A1-1'");
    $localOfficeId = $stmt->fetchColumn();
    $users[4][4] = $localOfficeId;

    $stmt = $pdo->prepare("
        INSERT INTO users (full_name, username, password, permissions, office_id)
        VALUES (?, ?, ?, ?, ?)
    ");

    foreach ($users as $user) {
        $stmt->execute($user);
    }

    // Insert default report template
    $stmt = $pdo->prepare("
        INSERT IGNORE INTO office_report_templates (name, description, fields, office_level)
        VALUES (?, ?, ?, ?)
    ");
    $stmt->execute([
        'Daily Operations Report',
        'Standard daily numerical reporting for all offices',
        json_encode([
            'incidents_reported' => ['type' => 'number', 'label' => 'Incidents Reported', 'required' => true],
            'tasks_completed' => ['type' => 'number', 'label' => 'Tasks Completed', 'required' => true],
            'calls_handled' => ['type' => 'number', 'label' => 'Calls Handled', 'required' => true],
            'work_hours' => ['type' => 'number', 'label' => 'Work Hours', 'required' => true, 'step' => '0.1'],
            'client_contacts' => ['type' => 'number', 'label' => 'Client Contacts', 'required' => true],
            'documents_processed' => ['type' => 'number', 'label' => 'Documents Processed', 'required' => true],
            'meetings_attended' => ['type' => 'number', 'label' => 'Meetings Attended', 'required' => true],
            'issues_resolved' => ['type' => 'number', 'label' => 'Issues Resolved', 'required' => true]
        ]),
        1
    ]);
}

// Get user's office and access level (updated for new schema)
function getUserOfficeInfo(PDO $pdo, string $username): array {
    try {
        // First check if the new schema tables exist
        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
        if ($stmt->rowCount() == 0) {
            throw new Exception("New schema not ready");
        }

        $stmt = $pdo->prepare("
            SELECT u.user_id, u.full_name, u.permissions as user_permissions,
                   o.office_id, o.name as office_name, o.level as office_level,
                   o.location, o.phone_number, o.permissions as office_permissions, o.parent_office_id
            FROM users u
            JOIN offices o ON u.office_id = o.office_id
            WHERE u.username = ?
        ");
        $stmt->execute([$username]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result) {
            return [
                'user_id' => $result['user_id'],
                'office_id' => $result['office_id'],
                'office_name' => $result['office_name'],
                'office_level' => $result['office_level'],
                'parent_office_id' => $result['parent_office_id'],
                'access_level' => $result['office_level'], // Level 1-4 maps directly to access level
                'location' => $result['location'],
                'phone_number' => $result['phone_number'],
                'user_permissions' => $result['user_permissions'],
                'office_permissions' => $result['office_permissions']
            ];
        }
    } catch (Exception $e) {
        // Fall back to default user if new schema isn't ready or user doesn't exist
    }

    // Fallback: Create default user info based on username
    $defaultUsers = [
        'admin' => [
            'user_id' => 1,
            'office_id' => 1,
            'office_name' => 'Head Office',
            'office_level' => 4,
            'access_level' => 4,
            'location' => 'HQ, New York',
            'phone_number' => '555-0100'
        ],
        'regmanager' => [
            'user_id' => 2,
            'office_id' => 2,
            'office_name' => 'Region A',
            'office_level' => 3,
            'access_level' => 3,
            'location' => 'Chicago',
            'phone_number' => '555-0101'
        ],
        'janesmith' => [
            'user_id' => 3,
            'office_id' => 4,
            'office_name' => 'Branch A1',
            'office_level' => 2,
            'access_level' => 2,
            'location' => 'Chicago North',
            'phone_number' => '555-0102'
        ],
        'johndoe' => [
            'user_id' => 4,
            'office_id' => 7,
            'office_name' => 'Office A1-1',
            'office_level' => 1,
            'access_level' => 1,
            'location' => 'Downtown Chicago',
            'phone_number' => '555-0103'
        ]
    ];

    $userDefaults = $defaultUsers[$username] ?? $defaultUsers['admin'];

    return [
        'user_id' => $userDefaults['user_id'],
        'office_id' => $userDefaults['office_id'],
        'office_name' => $userDefaults['office_name'],
        'office_level' => $userDefaults['office_level'],
        'parent_office_id' => null,
        'access_level' => $userDefaults['access_level'],
        'location' => $userDefaults['location'],
        'phone_number' => $userDefaults['phone_number'],
        'user_permissions' => '{"read_write": true}',
        'office_permissions' => '{"local_access": true}'
    ];
}

// Get office hierarchy tree (updated for new schema)
function getOfficeHierarchy(PDO $pdo, int $accessLevel, ?int $officeId = null): array {
    try {
        // Check if offices table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'offices'");
        if ($stmt->rowCount() == 0) {
            return getDefaultHierarchy();
        }

        $query = "
            WITH RECURSIVE office_tree AS (
                SELECT office_id, name, level, parent_office_id, location, phone_number,
                       0 as tree_level, CAST(office_id AS CHAR(200)) as path
                FROM offices
                WHERE parent_office_id IS NULL

                UNION ALL

                SELECT o.office_id, o.name, o.level, o.parent_office_id, o.location, o.phone_number,
                       ot.tree_level + 1, CONCAT(ot.path, '->', o.office_id) as path
                FROM offices o
                INNER JOIN office_tree ot ON o.parent_office_id = ot.office_id
            )
            SELECT * FROM office_tree ORDER BY path
        ";

        $stmt = $pdo->query($query);
        $offices = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // If no offices found, return default
        if (empty($offices)) {
            return getDefaultHierarchy();
        }

        // Filter based on access level and user's office
        if ($accessLevel < 4 && $officeId) {
            $allowedOffices = getAccessibleOffices($pdo, $officeId, $accessLevel);
            $offices = array_filter($offices, function($office) use ($allowedOffices) {
                return in_array($office['office_id'], $allowedOffices);
            });
        }

        return buildHierarchyTree($offices, 'office_id', 'parent_office_id');
    } catch (Exception $e) {
        return getDefaultHierarchy();
    }
}

// Get default hierarchy when database isn't ready
function getDefaultHierarchy(): array {
    return [
        [
            'office_id' => 1,
            'name' => 'Head Office',
            'level' => 4,
            'location' => 'HQ, New York',
            'phone_number' => '555-0100',
            'children' => [
                [
                    'office_id' => 2,
                    'name' => 'Region A',
                    'level' => 3,
                    'location' => 'Chicago',
                    'phone_number' => '555-0101',
                    'children' => [
                        [
                            'office_id' => 4,
                            'name' => 'Branch A1',
                            'level' => 2,
                            'location' => 'Chicago North',
                            'phone_number' => '555-0102',
                            'children' => [
                                [
                                    'office_id' => 7,
                                    'name' => 'Office A1-1',
                                    'level' => 1,
                                    'location' => 'Downtown Chicago',
                                    'phone_number' => '555-0103',
                                    'children' => []
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ]
    ];
}

// Get offices accessible to user based on their level
function getAccessibleOffices(PDO $pdo, int $officeId, int $accessLevel): array {
    try {
        // Check if offices table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'offices'");
        if ($stmt->rowCount() == 0) {
            return [$officeId]; // Return just the user's office if table doesn't exist
        }

        $accessible = [$officeId];

        if ($accessLevel >= 2) {
            // Can see child offices
            $stmt = $pdo->prepare("
                WITH RECURSIVE children AS (
                    SELECT office_id FROM offices WHERE office_id = ?
                    UNION ALL
                    SELECT o.office_id FROM offices o
                    INNER JOIN children c ON o.parent_office_id = c.office_id
                )
                SELECT office_id FROM children
            ");
            $stmt->execute([$officeId]);
            $accessible = $stmt->fetchAll(PDO::FETCH_COLUMN);
        }

        return $accessible;
    } catch (Exception $e) {
        return [$officeId]; // Fallback to just the user's office
    }
}

// Build hierarchical tree structure (updated for flexible field names)
function buildHierarchyTree(array $items, string $idField = 'office_id', string $parentField = 'parent_office_id', ?int $parentId = null): array {
    $tree = [];
    foreach ($items as $item) {
        if ($item[$parentField] == $parentId) {
            $item['children'] = buildHierarchyTree($items, $idField, $parentField, $item[$idField]);
            $tree[] = $item;
        }
    }
    return $tree;
}

// Initialize tables
initializeReportingTables($pdo);

// Get current user info (updated for new schema)
// For backward compatibility, we'll try to map badge_id to username
$username = $_SESSION['badge_id'] ?? $_SESSION['username'] ?? 'admin'; // Fallback to admin for testing
$userInfo = getUserOfficeInfo($pdo, $username);
$officeHierarchy = getOfficeHierarchy($pdo, $userInfo['access_level'], $userInfo['office_id']);

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    try {
        switch ($_POST['action']) {
            case 'submit_report':
                $reportDate = $_POST['report_date'] ?? date('Y-m-d');
                $officeId = (int)($_POST['office_id'] ?? $userInfo['office_id']);
                $reportData = $_POST['report_data'] ?? [];

                // Validate access
                $accessibleOffices = getAccessibleOffices($pdo, $userInfo['office_id'], $userInfo['access_level']);
                if (!in_array($officeId, $accessibleOffices)) {
                    throw new Exception('Access denied to this office');
                }

                // Validate numerical data
                foreach ($reportData as $key => $value) {
                    if (!is_numeric($value)) {
                        throw new Exception("Invalid numerical value for {$key}");
                    }
                    $reportData[$key] = (float)$value;
                }

                // Check if report exists for this date/office
                $stmt = $pdo->prepare("
                    SELECT id, version FROM office_daily_reports
                    WHERE report_date = ? AND office_id = ? AND is_current = TRUE
                ");
                $stmt->execute([$reportDate, $officeId]);
                $existing = $stmt->fetch(PDO::FETCH_ASSOC);

                $pdo->beginTransaction();

                if ($existing) {
                    // Create new version
                    $newVersion = $existing['version'] + 1;

                    // Mark current as not current
                    $stmt = $pdo->prepare("
                        UPDATE office_daily_reports
                        SET is_current = FALSE
                        WHERE id = ?
                    ");
                    $stmt->execute([$existing['id']]);

                    // Insert new version
                    $stmt = $pdo->prepare("
                        INSERT INTO office_daily_reports
                        (report_date, office_id, user_id, report_data, version, created_by, edited_by, edited_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
                    ");
                    $stmt->execute([
                        $reportDate, $officeId, $userInfo['user_id'],
                        json_encode($reportData), $newVersion, $userInfo['user_id'], $userInfo['user_id']
                    ]);

                    $action = 'REPORT_EDITED';
                } else {
                    // Insert new report
                    $stmt = $pdo->prepare("
                        INSERT INTO office_daily_reports
                        (report_date, office_id, user_id, report_data, created_by)
                        VALUES (?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $reportDate, $officeId, $userInfo['user_id'],
                        json_encode($reportData), $userInfo['user_id']
                    ]);

                    $action = 'REPORT_SUBMITTED';
                }

                // Update aggregations
                updateOfficeAggregations($pdo, $reportDate, $officeId);

                // History log (using new schema)
                $historyStmt = $pdo->prepare("
                    INSERT INTO history (entity_type, entity_id, change_type, new_value, changed_by)
                    VALUES ('user', ?, ?, ?, ?)
                ");
                $historyStmt->execute([
                    $userInfo['user_id'],
                    $action,
                    json_encode([
                        'report_date' => $reportDate,
                        'office_id' => $officeId,
                        'data_fields' => array_keys($reportData)
                    ]),
                    $userInfo['user_id']
                ]);

                $pdo->commit();
                echo json_encode(['success' => true, 'message' => 'Report saved successfully']);
                break;

            case 'get_reports':
                $startDate = $_POST['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
                $endDate = $_POST['end_date'] ?? date('Y-m-d');
                $officeId = (int)($_POST['office_id'] ?? $userInfo['office_id']);

                // Validate access
                $accessibleOffices = getAccessibleOffices($pdo, $userInfo['office_id'], $userInfo['access_level']);
                if (!in_array($officeId, $accessibleOffices)) {
                    throw new Exception('Access denied to this office');
                }

                $stmt = $pdo->prepare("
                    SELECT dr.*, o.name as office_name, o.level as office_level,
                           u.full_name as user_name, u.username,
                           ed.full_name as edited_by_name
                    FROM office_daily_reports dr
                    JOIN offices o ON dr.office_id = o.office_id
                    JOIN users u ON dr.user_id = u.user_id
                    LEFT JOIN users ed ON dr.edited_by = ed.user_id
                    WHERE dr.report_date BETWEEN ? AND ?
                    AND dr.office_id = ?
                    AND dr.is_current = TRUE
                    ORDER BY dr.report_date DESC
                ");
                $stmt->execute([$startDate, $endDate, $officeId]);
                $reports = $stmt->fetchAll(PDO::FETCH_ASSOC);

                // Decode JSON data
                foreach ($reports as &$report) {
                    $report['report_data'] = json_decode($report['report_data'], true);
                    $report['is_edited'] = $report['version'] > 1;
                }

                echo json_encode(['success' => true, 'reports' => $reports]);
                break;

            case 'get_aggregated':
                $startDate = $_POST['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
                $endDate = $_POST['end_date'] ?? date('Y-m-d');
                $officeId = (int)($_POST['office_id'] ?? $userInfo['office_id']);

                // Get aggregated data for office and date range
                $stmt = $pdo->prepare("
                    SELECT ar.*, o.name as office_name, o.level as office_level
                    FROM office_aggregated_reports ar
                    JOIN offices o ON ar.office_id = o.office_id
                    WHERE ar.report_date BETWEEN ? AND ?
                    AND ar.office_id = ?
                    ORDER BY ar.report_date DESC
                ");
                $stmt->execute([$startDate, $endDate, $officeId]);
                $aggregated = $stmt->fetchAll(PDO::FETCH_ASSOC);

                foreach ($aggregated as &$agg) {
                    $agg['aggregated_data'] = json_decode($agg['aggregated_data'], true);
                }

                echo json_encode(['success' => true, 'aggregated' => $aggregated]);
                break;

            case 'get_report_history':
                $reportDate = $_POST['report_date'] ?? date('Y-m-d');
                $officeId = (int)($_POST['office_id'] ?? $userInfo['office_id']);

                $stmt = $pdo->prepare("
                    SELECT dr.*, u.full_name as user_name, ed.full_name as edited_by_name
                    FROM office_daily_reports dr
                    JOIN users u ON dr.user_id = u.user_id
                    LEFT JOIN users ed ON dr.edited_by = ed.user_id
                    WHERE dr.report_date = ? AND dr.office_id = ?
                    ORDER BY dr.version DESC
                ");
                $stmt->execute([$reportDate, $officeId]);
                $history = $stmt->fetchAll(PDO::FETCH_ASSOC);

                foreach ($history as &$h) {
                    $h['report_data'] = json_decode($h['report_data'], true);
                }

                echo json_encode(['success' => true, 'history' => $history]);
                break;

            default:
                throw new Exception('Invalid action');
        }
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit;
}

// Function to update aggregations up the hierarchy (updated for new schema)
function updateOfficeAggregations(PDO $pdo, string $reportDate, int $officeId): void {
    // Get office hierarchy path
    $stmt = $pdo->prepare("
        WITH RECURSIVE office_path AS (
            SELECT office_id, parent_office_id, 0 as level
            FROM offices WHERE office_id = ?

            UNION ALL

            SELECT o.office_id, o.parent_office_id, op.level + 1
            FROM offices o
            INNER JOIN office_path op ON o.office_id = op.parent_office_id
        )
        SELECT office_id FROM office_path WHERE level > 0 ORDER BY level
    ");
    $stmt->execute([$officeId]);
    $parentOffices = $stmt->fetchAll(PDO::FETCH_COLUMN);

    // Update aggregations for each parent office
    foreach ($parentOffices as $parentId) {
        updateOfficeAggregation($pdo, $reportDate, $parentId);
    }

    // Also update the current office's aggregation
    updateOfficeAggregation($pdo, $reportDate, $officeId);
}

function updateOfficeAggregation(PDO $pdo, string $reportDate, int $officeId): void {
    // Get all child offices
    $stmt = $pdo->prepare("
        WITH RECURSIVE children AS (
            SELECT office_id FROM offices WHERE office_id = ?
            UNION ALL
            SELECT o.office_id FROM offices o
            INNER JOIN children c ON o.parent_office_id = c.office_id
        )
        SELECT DISTINCT c.office_id
        FROM children c
        JOIN office_daily_reports dr ON c.office_id = dr.office_id
        WHERE dr.report_date = ? AND dr.is_current = TRUE
    ");
    $stmt->execute([$officeId, $reportDate]);
    $childOffices = $stmt->fetchAll(PDO::FETCH_COLUMN);

    if (empty($childOffices)) {
        return;
    }

    // Aggregate data from all child offices
    $placeholders = str_repeat('?,', count($childOffices) - 1) . '?';
    $stmt = $pdo->prepare("
        SELECT report_data
        FROM office_daily_reports
        WHERE office_id IN ($placeholders)
        AND report_date = ?
        AND is_current = TRUE
    ");
    $stmt->execute([...$childOffices, $reportDate]);
    $reports = $stmt->fetchAll(PDO::FETCH_COLUMN);

    // Sum all numerical values
    $aggregated = [];
    foreach ($reports as $reportJson) {
        $data = json_decode($reportJson, true);
        foreach ($data as $key => $value) {
            if (is_numeric($value)) {
                $aggregated[$key] = ($aggregated[$key] ?? 0) + (float)$value;
            }
        }
    }

    // Insert or update aggregated report
    $stmt = $pdo->prepare("
        INSERT INTO office_aggregated_reports (report_date, office_id, aggregated_data, last_updated)
        VALUES (?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE
        aggregated_data = VALUES(aggregated_data),
        last_updated = NOW()
    ");
    $stmt->execute([$reportDate, $officeId, json_encode($aggregated)]);
}

// Get report template (updated for new schema)
function getReportTemplate(PDO $pdo): array {
    $stmt = $pdo->prepare("
        SELECT fields FROM office_report_templates
        WHERE is_active = TRUE
        ORDER BY id LIMIT 1
    ");
    $stmt->execute();
    $template = $stmt->fetch(PDO::FETCH_ASSOC);

    return $template ? json_decode($template['fields'], true) : [];
}

$reportTemplate = getReportTemplate($pdo);

// Function to render office tree HTML (updated for new schema)
function renderOfficeTree(array $offices, int $level = 1): void {
    foreach ($offices as $office) {
        $levelClass = "office-level-{$level}";
        $typeIcon = match($office['level']) {
            4 => 'fas fa-building',      // Head Office
            3 => 'fas fa-home',          // Region
            2 => 'fas fa-map',           // Branch
            1 => 'fas fa-map-marker-alt', // Office
            default => 'fas fa-circle'
        };

        $levelName = match($office['level']) {
            4 => 'Head Office',
            3 => 'Region',
            2 => 'Branch',
            1 => 'Office',
            default => 'Level ' . $office['level']
        };

        echo "<div class='office-node {$levelClass}' data-office-id='{$office['office_id']}' data-office-level='{$office['level']}'>";
        echo "<i class='{$typeIcon} me-2'></i>";
        echo "<strong>" . htmlspecialchars($office['name']) . "</strong>";
        echo "<span class='badge bg-secondary ms-2'>" . htmlspecialchars($levelName) . "</span>";
        echo "</div>";

        if (!empty($office['children'])) {
            renderOfficeTree($office['children'], $level + 1);
        }
    }
}
?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hierarchical Reporting System - Police Portal Pro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --police-blue: #002366;
            --police-gold: #FFD700;
            --police-light-blue: #1e3a8a;
            --police-dark-blue: #001122;
        }

        body {
            background: linear-gradient(135deg, var(--police-blue) 0%, var(--police-light-blue) 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            margin: 20px auto;
            padding: 30px;
        }

        .police-header {
            background: linear-gradient(90deg, var(--police-blue), var(--police-dark-blue));
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .police-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100%;
            background: linear-gradient(45deg, transparent, var(--police-gold));
            opacity: 0.1;
        }

        .police-badge {
            background: var(--police-gold);
            color: var(--police-blue);
            font-weight: bold;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
        }

        .office-tree {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
        }

        .office-node {
            cursor: pointer;
            padding: 8px 12px;
            margin: 2px 0;
            border-radius: 5px;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .office-node:hover {
            background: var(--police-gold);
            color: var(--police-blue);
            border-left-color: var(--police-blue);
        }

        .office-node.active {
            background: var(--police-blue);
            color: white;
            border-left-color: var(--police-gold);
        }

        .office-level-1 { margin-left: 0px; }
        .office-level-2 { margin-left: 20px; }
        .office-level-3 { margin-left: 40px; }
        .office-level-4 { margin-left: 60px; }

        .report-form {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .form-control:focus {
            border-color: var(--police-blue);
            box-shadow: 0 0 0 0.2rem rgba(0, 35, 102, 0.25);
        }

        .btn-police {
            background: var(--police-blue);
            border-color: var(--police-blue);
            color: white;
        }

        .btn-police:hover {
            background: var(--police-dark-blue);
            border-color: var(--police-dark-blue);
            color: white;
        }

        .btn-police-gold {
            background: var(--police-gold);
            border-color: var(--police-gold);
            color: var(--police-blue);
            font-weight: bold;
        }

        .btn-police-gold:hover {
            background: #e6c200;
            border-color: #e6c200;
            color: var(--police-blue);
        }

        .report-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .report-card:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .report-edited {
            border-left: 4px solid #ffc107;
        }

        .report-original {
            border-left: 4px solid #28a745;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: var(--police-blue);
        }

        .loading i {
            font-size: 2em;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert-police {
            background: rgba(0, 35, 102, 0.1);
            border: 1px solid var(--police-blue);
            color: var(--police-blue);
        }

        .table-police th {
            background: var(--police-blue);
            color: white;
            border: none;
        }

        .table-police td {
            border-color: #dee2e6;
        }

        .nav-tabs .nav-link.active {
            background: var(--police-blue);
            border-color: var(--police-blue);
            color: white;
        }

        .nav-tabs .nav-link {
            color: var(--police-blue);
            border-color: transparent;
        }

        .nav-tabs .nav-link:hover {
            border-color: var(--police-gold);
            color: var(--police-dark-blue);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="police-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="mb-0">
                            <i class="fas fa-chart-line me-3"></i>
                            Hierarchical Reporting System
                        </h1>
                        <p class="mb-0 mt-2 opacity-75">
                            Secure daily numerical data submissions with versioned edits and aggregated roll-ups
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="police-badge">
                            <i class="fas fa-badge-check me-2"></i>
                            <?= htmlspecialchars($username) ?>
                        </div>
                        <div class="text-white-50 mt-2">
                            <small>
                                <i class="fas fa-map-marker-alt me-1"></i>
                                <?= htmlspecialchars($userInfo['office_name']) ?>
                                (Level <?= (int)$userInfo['office_level'] ?>)
                            </small>
                        </div>
                        <div class="text-white-50">
                            <small>
                                <i class="fas fa-shield-alt me-1"></i>
                                Access Level <?= (int)$userInfo['access_level'] ?>
                            </small>
                        </div>
                        <div class="text-white-50">
                            <small>
                                <i class="fas fa-phone me-1"></i>
                                <?= htmlspecialchars((string)($userInfo['phone_number'] ?: 'N/A')) ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="submit-tab" data-bs-toggle="tab" data-bs-target="#submit-panel" type="button" role="tab">
                        <i class="fas fa-plus-circle me-2"></i>Submit Report
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="view-tab" data-bs-toggle="tab" data-bs-target="#view-panel" type="button" role="tab">
                        <i class="fas fa-table me-2"></i>View Reports
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="hierarchy-tab" data-bs-toggle="tab" data-bs-target="#hierarchy-panel" type="button" role="tab">
                        <i class="fas fa-sitemap me-2"></i>Station Hierarchy
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="aggregated-tab" data-bs-toggle="tab" data-bs-target="#aggregated-panel" type="button" role="tab">
                        <i class="fas fa-chart-bar me-2"></i>Aggregated Data
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="audit-tab" data-bs-toggle="tab" data-bs-target="#audit-panel" type="button" role="tab">
                        <i class="fas fa-history me-2"></i>Audit Trail
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="mainTabContent">

                <!-- Submit Report Panel -->
                <div class="tab-pane fade show active" id="submit-panel" role="tabpanel">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="report-form">
                                <h4 class="mb-3">
                                    <i class="fas fa-file-alt me-2 text-primary"></i>
                                    Daily Operations Report
                                </h4>

                                <form id="reportForm">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="reportDate" class="form-label">Report Date</label>
                                            <input type="date" class="form-control" id="reportDate"
                                                   value="<?= date('Y-m-d') ?>" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="reportOffice" class="form-label">Office</label>
                                            <select class="form-select" id="reportOffice" required>
                                                <?php if ($userInfo['office_id']): ?>
                                                    <option value="<?= $userInfo['office_id'] ?>" selected>
                                                        <?= htmlspecialchars($userInfo['office_name']) ?>
                                                    </option>
                                                <?php endif; ?>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="alert alert-police mb-3">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Instructions:</strong> Enter numerical data only. All fields are required.
                                        Edits will create new versions while preserving originals.
                                    </div>

                                    <div class="row">
                                        <?php foreach ($reportTemplate as $fieldKey => $field): ?>
                                            <div class="col-md-6 mb-3">
                                                <label for="field_<?= $fieldKey ?>" class="form-label">
                                                    <?= htmlspecialchars($field['label']) ?>
                                                    <?php if ($field['required']): ?>
                                                        <span class="text-danger">*</span>
                                                    <?php endif; ?>
                                                </label>
                                                <input type="number"
                                                       class="form-control"
                                                       id="field_<?= $fieldKey ?>"
                                                       name="<?= $fieldKey ?>"
                                                       step="<?= $field['step'] ?? '1' ?>"
                                                       min="0"
                                                       <?= $field['required'] ? 'required' : '' ?>>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>

                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <button type="button" class="btn btn-outline-secondary me-md-2" onclick="clearForm()">
                                            <i class="fas fa-eraser me-2"></i>Clear
                                        </button>
                                        <button type="submit" class="btn btn-police">
                                            <i class="fas fa-save me-2"></i>Submit Report
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">
                                        <i class="fas fa-clock me-2"></i>Recent Submissions
                                    </h6>
                                </div>
                                <div class="card-body" id="recentSubmissions">
                                    <div class="loading">
                                        <i class="fas fa-spinner"></i>
                                        <p>Loading recent reports...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- View Reports Panel -->
                <div class="tab-pane fade" id="view-panel" role="tabpanel">
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label for="viewStartDate" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="viewStartDate"
                                   value="<?= date('Y-m-d', strtotime('-30 days')) ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="viewEndDate" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="viewEndDate"
                                   value="<?= date('Y-m-d') ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="viewOffice" class="form-label">Office</label>
                            <select class="form-select" id="viewOffice">
                                <?php if ($userInfo['office_id']): ?>
                                    <option value="<?= $userInfo['office_id'] ?>" selected>
                                        <?= htmlspecialchars($userInfo['office_name']) ?>
                                    </option>
                                <?php endif; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-police d-block w-100" onclick="loadReports()">
                                <i class="fas fa-search me-2"></i>Search
                            </button>
                        </div>
                    </div>

                    <div id="reportsContainer">
                        <div class="loading">
                            <i class="fas fa-spinner"></i>
                            <p>Click Search to load reports...</p>
                        </div>
                    </div>
                </div>

                <!-- Station Hierarchy Panel -->
                <div class="tab-pane fade" id="hierarchy-panel" role="tabpanel">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-sitemap me-2"></i>Office Hierarchy
                                    </h6>
                                </div>
                                <div class="card-body p-0">
                                    <div class="office-tree" id="officeTree">
                                        <?php renderOfficeTree($officeHierarchy, 1); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div id="hierarchyDetails">
                                <div class="alert alert-police">
                                    <i class="fas fa-hand-pointer me-2"></i>
                                    Select a station from the hierarchy to view its reports and drill-down capabilities.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Aggregated Data Panel -->
                <div class="tab-pane fade" id="aggregated-panel" role="tabpanel">
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label for="aggStartDate" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="aggStartDate"
                                   value="<?= date('Y-m-d', strtotime('-30 days')) ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="aggEndDate" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="aggEndDate"
                                   value="<?= date('Y-m-d') ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="aggOffice" class="form-label">Office</label>
                            <select class="form-select" id="aggOffice">
                                <?php if ($userInfo['office_id']): ?>
                                    <option value="<?= $userInfo['office_id'] ?>" selected>
                                        <?= htmlspecialchars($userInfo['office_name']) ?>
                                    </option>
                                <?php endif; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-police d-block w-100" onclick="loadAggregatedData()">
                                <i class="fas fa-chart-bar me-2"></i>Load Data
                            </button>
                        </div>
                    </div>

                    <div id="aggregatedContainer">
                        <div class="loading">
                            <i class="fas fa-spinner"></i>
                            <p>Click Load Data to view aggregated reports...</p>
                        </div>
                    </div>
                </div>

                <!-- Audit Trail Panel -->
                <div class="tab-pane fade" id="audit-panel" role="tabpanel">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="auditDate" class="form-label">Report Date</label>
                            <input type="date" class="form-control" id="auditDate"
                                   value="<?= date('Y-m-d') ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="auditOffice" class="form-label">Office</label>
                            <select class="form-select" id="auditOffice">
                                <?php if ($userInfo['office_id']): ?>
                                    <option value="<?= $userInfo['office_id'] ?>" selected>
                                        <?= htmlspecialchars($userInfo['office_name']) ?>
                                    </option>
                                <?php endif; ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-police d-block w-100" onclick="loadAuditTrail()">
                                <i class="fas fa-history me-2"></i>Load History
                            </button>
                        </div>
                    </div>

                    <div id="auditContainer">
                        <div class="loading">
                            <i class="fas fa-spinner"></i>
                            <p>Click Load History to view report versions...</p>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Footer -->
            <div class="text-center mt-4 pt-3 border-top">
                <div class="row">
                    <div class="col-md-6">
                        <a href="/dashboard.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                    </div>
                    <div class="col-md-6 text-end">
                        <button type="button" class="btn btn-police-gold me-2" onclick="exportToExcel()">
                            <i class="fas fa-file-excel me-2"></i>Export Excel
                        </button>
                        <button type="button" class="btn btn-police-gold" onclick="exportToPDF()">
                            <i class="fas fa-file-pdf me-2"></i>Export PDF
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Global variables (updated for new schema)
        let currentUserInfo = <?= json_encode($userInfo) ?>;
        let reportTemplate = <?= json_encode($reportTemplate) ?>;
        let officeHierarchy = <?= json_encode($officeHierarchy) ?>;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadRecentSubmissions();
            populateOfficeSelects();
            setupEventListeners();
        });

        // Setup event listeners
        function setupEventListeners() {
            // Report form submission
            document.getElementById('reportForm').addEventListener('submit', function(e) {
                e.preventDefault();
                submitReport();
            });

            // Office tree clicks
            document.querySelectorAll('.office-node').forEach(node => {
                node.addEventListener('click', function() {
                    selectOffice(this);
                });
            });

            // Tab change events
            document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
                tab.addEventListener('shown.bs.tab', function(e) {
                    const target = e.target.getAttribute('data-bs-target');
                    if (target === '#view-panel') {
                        // Auto-load reports when switching to view tab
                        // loadReports();
                    }
                });
            });
        }

        // Populate office select dropdowns
        function populateOfficeSelects() {
            const selects = ['reportOffice', 'viewOffice', 'aggOffice', 'auditOffice'];

            selects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (select) {
                    populateOfficeSelect(select, officeHierarchy);
                }
            });
        }

        function populateOfficeSelect(select, offices, level = 0) {
            offices.forEach(office => {
                const indent = '&nbsp;'.repeat(level * 4);
                const option = document.createElement('option');
                option.value = office.office_id;
                const levelName = getLevelName(office.level);
                option.innerHTML = indent + office.name + ' (' + levelName + ')';

                // Check if user has access to this office
                if (hasOfficeAccess(office.office_id)) {
                    select.appendChild(option);
                }

                if (office.children && office.children.length > 0) {
                    populateOfficeSelect(select, office.children, level + 1);
                }
            });
        }

        // Get level name
        function getLevelName(level) {
            const levelNames = {4: 'Head Office', 3: 'Region', 2: 'Branch', 1: 'Office'};
            return levelNames[level] || 'Level ' + level;
        }

        // Check if user has access to office
        function hasOfficeAccess(officeId) {
            // For now, simplified access control
            // In production, this should check against server-side access rules
            return true;
        }

        // Submit report
        async function submitReport() {
            const form = document.getElementById('reportForm');
            const formData = new FormData();

            formData.append('action', 'submit_report');
            formData.append('report_date', document.getElementById('reportDate').value);
            formData.append('office_id', document.getElementById('reportOffice').value);

            // Collect report data
            const reportData = {};
            Object.keys(reportTemplate).forEach(fieldKey => {
                const field = document.querySelector(`[name="${fieldKey}"]`);
                if (field) {
                    reportData[fieldKey] = field.value;
                }
            });

            formData.append('report_data', JSON.stringify(reportData));

            try {
                showLoading('Submitting report...');
                const response = await fetch(window.location.href, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                hideLoading();

                if (result.success) {
                    showAlert('success', 'Report submitted successfully!');
                    form.reset();
                    document.getElementById('reportDate').value = new Date().toISOString().split('T')[0];
                    loadRecentSubmissions();
                } else {
                    showAlert('danger', 'Error: ' + result.error);
                }
            } catch (error) {
                hideLoading();
                showAlert('danger', 'Network error: ' + error.message);
            }
        }

        // Load recent submissions
        async function loadRecentSubmissions() {
            try {
                const formData = new FormData();
                formData.append('action', 'get_reports');
                formData.append('start_date', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]);
                formData.append('end_date', new Date().toISOString().split('T')[0]);
                formData.append('office_id', currentUserInfo.office_id);

                const response = await fetch(window.location.href, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    displayRecentSubmissions(result.reports);
                } else {
                    document.getElementById('recentSubmissions').innerHTML =
                        '<div class="alert alert-warning">No recent reports found.</div>';
                }
            } catch (error) {
                document.getElementById('recentSubmissions').innerHTML =
                    '<div class="alert alert-danger">Error loading recent reports.</div>';
            }
        }

        // Display recent submissions
        function displayRecentSubmissions(reports) {
            const container = document.getElementById('recentSubmissions');

            if (reports.length === 0) {
                container.innerHTML = '<div class="alert alert-info">No recent reports found.</div>';
                return;
            }

            let html = '';
            reports.slice(0, 5).forEach(report => {
                const editedBadge = report.is_edited ? '<span class="badge bg-warning text-dark ms-2">Edited</span>' : '';
                html += `
                    <div class="border-bottom pb-2 mb-2">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <strong>${report.report_date}</strong>${editedBadge}
                                <br><small class="text-muted">by ${report.user_name}</small>
                            </div>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewReportHistory('${report.report_date}', ${report.office_id})">
                                <i class="fas fa-history"></i>
                            </button>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // Load reports
        async function loadReports() {
            const startDate = document.getElementById('viewStartDate').value;
            const endDate = document.getElementById('viewEndDate').value;
            const officeId = document.getElementById('viewOffice').value;

            try {
                showLoadingInContainer('reportsContainer', 'Loading reports...');

                const formData = new FormData();
                formData.append('action', 'get_reports');
                formData.append('start_date', startDate);
                formData.append('end_date', endDate);
                formData.append('office_id', officeId);

                const response = await fetch(window.location.href, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    displayReports(result.reports);
                } else {
                    document.getElementById('reportsContainer').innerHTML =
                        '<div class="alert alert-warning">Error: ' + result.error + '</div>';
                }
            } catch (error) {
                document.getElementById('reportsContainer').innerHTML =
                    '<div class="alert alert-danger">Network error: ' + error.message + '</div>';
            }
        }

        // Display reports
        function displayReports(reports) {
            const container = document.getElementById('reportsContainer');

            if (reports.length === 0) {
                container.innerHTML = '<div class="alert alert-info">No reports found for the selected criteria.</div>';
                return;
            }

            let html = '<div class="row">';

            reports.forEach(report => {
                const editedClass = report.is_edited ? 'report-edited' : 'report-original';
                const editedBadge = report.is_edited ?
                    '<span class="badge bg-warning text-dark">Edited</span>' :
                    '<span class="badge bg-success">Original</span>';

                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card report-card ${editedClass}">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${report.report_date}</strong>
                                    ${editedBadge}
                                </div>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="editReport('${report.report_date}', ${report.office_id})" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-info" onclick="viewReportHistory('${report.report_date}', ${report.office_id})" title="History">
                                        <i class="fas fa-history"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                `;

                Object.entries(report.report_data).forEach(([key, value]) => {
                    const label = reportTemplate[key]?.label || key;
                    html += `
                        <div class="col-6 mb-2">
                            <small class="text-muted">${label}</small>
                            <div class="fw-bold">${value}</div>
                        </div>
                    `;
                });

                html += `
                                </div>
                                <hr>
                                <small class="text-muted">
                                    <i class="fas fa-user me-1"></i>${report.user_name} (${report.username})
                                    <br><i class="fas fa-clock me-1"></i>${formatDateTime(report.created_at)}
                `;

                if (report.edited_by_name) {
                    html += `<br><i class="fas fa-edit me-1"></i>Edited by ${report.edited_by_name} on ${formatDateTime(report.edited_at)}`;
                }

                html += `
                                </small>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            container.innerHTML = html;
        }

        // Load aggregated data
        async function loadAggregatedData() {
            const startDate = document.getElementById('aggStartDate').value;
            const endDate = document.getElementById('aggEndDate').value;
            const officeId = document.getElementById('aggOffice').value;

            try {
                showLoadingInContainer('aggregatedContainer', 'Loading aggregated data...');

                const formData = new FormData();
                formData.append('action', 'get_aggregated');
                formData.append('start_date', startDate);
                formData.append('end_date', endDate);
                formData.append('office_id', officeId);

                const response = await fetch(window.location.href, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    displayAggregatedData(result.aggregated);
                } else {
                    document.getElementById('aggregatedContainer').innerHTML =
                        '<div class="alert alert-warning">Error: ' + result.error + '</div>';
                }
            } catch (error) {
                document.getElementById('aggregatedContainer').innerHTML =
                    '<div class="alert alert-danger">Network error: ' + error.message + '</div>';
            }
        }

        // Display aggregated data
        function displayAggregatedData(aggregated) {
            const container = document.getElementById('aggregatedContainer');

            if (aggregated.length === 0) {
                container.innerHTML = '<div class="alert alert-info">No aggregated data found for the selected criteria.</div>';
                return;
            }

            // Create summary table
            let html = `
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Aggregated Reports Summary</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-police table-striped">
                                <thead>
                                    <tr>
                                        <th>Date</th>
            `;

            // Get field headers from first record
            if (aggregated.length > 0) {
                Object.keys(aggregated[0].aggregated_data).forEach(key => {
                    const label = reportTemplate[key]?.label || key;
                    html += `<th>${label}</th>`;
                });
            }

            html += `
                                        <th>Last Updated</th>
                                    </tr>
                                </thead>
                                <tbody>
            `;

            aggregated.forEach(agg => {
                html += `<tr><td><strong>${agg.report_date}</strong></td>`;

                Object.values(agg.aggregated_data).forEach(value => {
                    html += `<td>${value}</td>`;
                });

                html += `<td><small>${formatDateTime(agg.last_updated)}</small></td></tr>`;
            });

            html += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            container.innerHTML = html;
        }

        // Load audit trail
        async function loadAuditTrail() {
            const reportDate = document.getElementById('auditDate').value;
            const officeId = document.getElementById('auditOffice').value;

            try {
                showLoadingInContainer('auditContainer', 'Loading audit trail...');

                const formData = new FormData();
                formData.append('action', 'get_report_history');
                formData.append('report_date', reportDate);
                formData.append('office_id', officeId);

                const response = await fetch(window.location.href, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    displayAuditTrail(result.history);
                } else {
                    document.getElementById('auditContainer').innerHTML =
                        '<div class="alert alert-warning">Error: ' + result.error + '</div>';
                }
            } catch (error) {
                document.getElementById('auditContainer').innerHTML =
                    '<div class="alert alert-danger">Network error: ' + error.message + '</div>';
            }
        }

        // Display audit trail
        function displayAuditTrail(history) {
            const container = document.getElementById('auditContainer');

            if (history.length === 0) {
                container.innerHTML = '<div class="alert alert-info">No report history found for the selected date and station.</div>';
                return;
            }

            let html = '<div class="timeline">';

            history.forEach((version, index) => {
                const isLatest = index === 0;
                const badgeClass = isLatest ? 'bg-success' : 'bg-secondary';

                html += `
                    <div class="card mb-3">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <span class="badge ${badgeClass}">Version ${version.version}</span>
                                ${isLatest ? '<span class="badge bg-primary ms-2">Current</span>' : ''}
                            </div>
                            <small class="text-muted">${formatDateTime(version.created_at)}</small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                `;

                Object.entries(version.report_data).forEach(([key, value]) => {
                    const label = reportTemplate[key]?.label || key;
                    html += `
                        <div class="col-md-3 mb-2">
                            <small class="text-muted">${label}</small>
                            <div class="fw-bold">${value}</div>
                        </div>
                    `;
                });

                html += `
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-md-6">
                                    <small><i class="fas fa-user me-1"></i><strong>Created by:</strong> ${version.user_name}</small>
                                </div>
                `;

                if (version.edited_by_name) {
                    html += `
                        <div class="col-md-6">
                            <small><i class="fas fa-edit me-1"></i><strong>Edited by:</strong> ${version.edited_by_name}</small>
                        </div>
                    `;
                }

                html += `
                            </div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            container.innerHTML = html;
        }

        // View report history modal
        async function viewReportHistory(reportDate, officeId) {
            const modal = new bootstrap.Modal(document.getElementById('historyModal'));

            try {
                document.getElementById('historyModalBody').innerHTML =
                    '<div class="loading"><i class="fas fa-spinner"></i><p>Loading history...</p></div>';

                modal.show();

                const formData = new FormData();
                formData.append('action', 'get_report_history');
                formData.append('report_date', reportDate);
                formData.append('office_id', officeId);

                const response = await fetch(window.location.href, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    displayHistoryModal(result.history);
                } else {
                    document.getElementById('historyModalBody').innerHTML =
                        '<div class="alert alert-warning">Error: ' + result.error + '</div>';
                }
            } catch (error) {
                document.getElementById('historyModalBody').innerHTML =
                    '<div class="alert alert-danger">Network error: ' + error.message + '</div>';
            }
        }

        // Display history in modal
        function displayHistoryModal(history) {
            const container = document.getElementById('historyModalBody');

            if (history.length === 0) {
                container.innerHTML = '<div class="alert alert-info">No history found.</div>';
                return;
            }

            let html = '';
            history.forEach((version, index) => {
                const isLatest = index === 0;
                html += `
                    <div class="border-bottom pb-3 mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <span class="badge ${isLatest ? 'bg-success' : 'bg-secondary'}">Version ${version.version}</span>
                                ${isLatest ? '<span class="badge bg-primary ms-2">Current</span>' : ''}
                            </div>
                            <small class="text-muted">${formatDateTime(version.created_at)}</small>
                        </div>
                        <div class="row">
                `;

                Object.entries(version.report_data).forEach(([key, value]) => {
                    const label = reportTemplate[key]?.label || key;
                    html += `
                        <div class="col-6 mb-1">
                            <small class="text-muted">${label}:</small> <strong>${value}</strong>
                        </div>
                    `;
                });

                html += `
                        </div>
                        <small class="text-muted">
                            <i class="fas fa-user me-1"></i>${version.user_name}
                `;

                if (version.edited_by_name) {
                    html += ` | <i class="fas fa-edit me-1"></i>Edited by ${version.edited_by_name}`;
                }

                html += '</small></div>';
            });

            container.innerHTML = html;
        }

        // Edit report
        async function editReport(reportDate, officeId) {
            try {
                // Load current report data
                const formData = new FormData();
                formData.append('action', 'get_reports');
                formData.append('start_date', reportDate);
                formData.append('end_date', reportDate);
                formData.append('office_id', officeId);

                const response = await fetch(window.location.href, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success && result.reports.length > 0) {
                    const report = result.reports[0];
                    populateEditForm(report);

                    const modal = new bootstrap.Modal(document.getElementById('editModal'));
                    modal.show();
                } else {
                    showAlert('warning', 'Report not found or access denied.');
                }
            } catch (error) {
                showAlert('danger', 'Error loading report: ' + error.message);
            }
        }

        // Populate edit form
        function populateEditForm(report) {
            document.getElementById('editReportDate').value = report.report_date;
            document.getElementById('editOfficeId').value = report.office_id;

            let html = '';
            Object.entries(report.report_data).forEach(([key, value]) => {
                const field = reportTemplate[key];
                if (field) {
                    html += `
                        <div class="col-md-6 mb-3">
                            <label for="edit_${key}" class="form-label">
                                ${field.label}
                                ${field.required ? '<span class="text-danger">*</span>' : ''}
                            </label>
                            <input type="number"
                                   class="form-control"
                                   id="edit_${key}"
                                   name="${key}"
                                   value="${value}"
                                   step="${field.step || '1'}"
                                   min="0"
                                   ${field.required ? 'required' : ''}>
                        </div>
                    `;
                }
            });

            document.getElementById('editFormFields').innerHTML = html;
        }

        // Save edited report
        async function saveEditedReport() {
            const reportDate = document.getElementById('editReportDate').value;
            const officeId = document.getElementById('editOfficeId').value;

            const formData = new FormData();
            formData.append('action', 'submit_report');
            formData.append('report_date', reportDate);
            formData.append('office_id', officeId);

            // Collect edited data
            const reportData = {};
            Object.keys(reportTemplate).forEach(fieldKey => {
                const field = document.getElementById(`edit_${fieldKey}`);
                if (field) {
                    reportData[fieldKey] = field.value;
                }
            });

            formData.append('report_data', JSON.stringify(reportData));

            try {
                const response = await fetch(window.location.href, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('success', 'Report updated successfully!');
                    bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
                    loadReports(); // Refresh the reports view
                    loadRecentSubmissions(); // Refresh recent submissions
                } else {
                    showAlert('danger', 'Error: ' + result.error);
                }
            } catch (error) {
                showAlert('danger', 'Network error: ' + error.message);
            }
        }

        // Office selection
        function selectOffice(element) {
            // Remove active class from all offices
            document.querySelectorAll('.office-node').forEach(node => {
                node.classList.remove('active');
            });

            // Add active class to selected office
            element.classList.add('active');

            const officeId = element.getAttribute('data-office-id');
            const officeLevel = element.getAttribute('data-office-level');

            // Load office details
            loadOfficeDetails(officeId, officeLevel);
        }

        // Load office details
        async function loadOfficeDetails(officeId, officeLevel) {
            const container = document.getElementById('hierarchyDetails');

            try {
                showLoadingInContainer('hierarchyDetails', 'Loading office details...');

                const formData = new FormData();
                formData.append('action', 'get_reports');
                formData.append('start_date', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]);
                formData.append('end_date', new Date().toISOString().split('T')[0]);
                formData.append('office_id', officeId);

                const response = await fetch(window.location.href, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    displayOfficeDetails(officeId, officeLevel, result.reports);
                } else {
                    container.innerHTML = '<div class="alert alert-warning">Error loading office details.</div>';
                }
            } catch (error) {
                container.innerHTML = '<div class="alert alert-danger">Network error: ' + error.message + '</div>';
            }
        }

        // Display office details
        function displayOfficeDetails(officeId, officeLevel, reports) {
            const container = document.getElementById('hierarchyDetails');

            let html = `
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>Office Details
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>Office Level:</strong> ${getLevelName(officeLevel)}
                            </div>
                            <div class="col-md-6">
                                <strong>Reports (Last 30 days):</strong> ${reports.length}
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button class="btn btn-police me-md-2" onclick="drillDownReports(${officeId})">
                                <i class="fas fa-search me-2"></i>View All Reports
                            </button>
                            <button class="btn btn-police-gold" onclick="viewAggregatedForOffice(${officeId})">
                                <i class="fas fa-chart-bar me-2"></i>View Aggregated
                            </button>
                        </div>
                    </div>
                </div>
            `;

            if (reports.length > 0) {
                html += `
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">Recent Reports Summary</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                `;

                reports.slice(0, 3).forEach(report => {
                    const editedBadge = report.is_edited ? '<span class="badge bg-warning text-dark ms-2">Edited</span>' : '';
                    html += `
                        <div class="col-md-4 mb-2">
                            <div class="border rounded p-2">
                                <strong>${report.report_date}</strong>${editedBadge}
                                <br><small class="text-muted">by ${report.user_name}</small>
                            </div>
                        </div>
                    `;
                });

                html += `
                            </div>
                        </div>
                    </div>
                `;
            }

            container.innerHTML = html;
        }

        // Drill down reports
        function drillDownReports(officeId) {
            // Switch to view reports tab and set office
            document.getElementById('view-tab').click();
            document.getElementById('viewOffice').value = officeId;
            loadReports();
        }

        // View aggregated for office
        function viewAggregatedForOffice(officeId) {
            // Switch to aggregated tab and set office
            document.getElementById('aggregated-tab').click();
            document.getElementById('aggOffice').value = officeId;
            loadAggregatedData();
        }

        // Utility functions
        function clearForm() {
            document.getElementById('reportForm').reset();
            document.getElementById('reportDate').value = new Date().toISOString().split('T')[0];
        }

        function formatDateTime(dateTimeString) {
            return new Date(dateTimeString).toLocaleString();
        }

        function showAlert(type, message) {
            // Create and show bootstrap alert
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            // Insert at top of main container
            const mainContainer = document.querySelector('.main-container');
            mainContainer.insertBefore(alertDiv, mainContainer.firstChild);

            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        function showLoading(message) {
            // Implementation for global loading indicator
            console.log('Loading:', message);
        }

        function hideLoading() {
            // Implementation for hiding global loading indicator
            console.log('Loading complete');
        }

        function showLoadingInContainer(containerId, message) {
            const container = document.getElementById(containerId);
            container.innerHTML = `
                <div class="loading">
                    <i class="fas fa-spinner"></i>
                    <p>${message}</p>
                </div>
            `;
        }

        // Export functions (placeholder implementations)
        function exportToExcel() {
            showAlert('info', 'Excel export functionality will be implemented with PhpSpreadsheet.');
        }

        function exportToPDF() {
            showAlert('info', 'PDF export functionality will be implemented with TCPDF.');
        }
    </script>

    <!-- Modals -->
    <!-- Report History Modal -->
    <div class="modal fade" id="historyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-history me-2"></i>Report Version History
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="historyModalBody">
                    <div class="loading">
                        <i class="fas fa-spinner"></i>
                        <p>Loading history...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Report Modal -->
    <div class="modal fade" id="editModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>Edit Report
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editReportForm">
                        <input type="hidden" id="editReportDate">
                        <input type="hidden" id="editStationId">

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Note:</strong> Editing will create a new version while preserving the original.
                            Changes will be marked as "(edited)" and logged in the audit trail.
                        </div>

                        <div class="row" id="editFormFields">
                            <!-- Fields will be populated dynamically -->
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-police" onclick="saveEditedReport()">
                        <i class="fas fa-save me-2"></i>Save Changes
                    </button>
                </div>
            </div>
        </div>
    </div>

</body>
</html>
    <!-- Report History Modal -->
    <div class="modal fade" id="historyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-history me-2"></i>Report Version History
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="historyModalBody">
                    <div class="loading">
                        <i class="fas fa-spinner"></i>
                        <p>Loading history...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Report Modal -->
    <div class="modal fade" id="editModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>Edit Report
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editReportForm">
                        <input type="hidden" id="editReportDate">
                        <input type="hidden" id="editOfficeId">

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Note:</strong> Editing will create a new version while preserving the original.
                            Changes will be marked as "(edited)" and logged in the audit trail.
                        </div>

                        <div class="row" id="editFormFields">
                            <!-- Fields will be populated dynamically -->
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-police" onclick="saveEditedReport()">
                        <i class="fas fa-save me-2"></i>Save Changes
                    </button>
                </div>
            </div>
        </div>
    </div>
