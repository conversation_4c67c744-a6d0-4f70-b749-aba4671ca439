<?php
// User: My Forms (placeholder)
declare(strict_types=1);
require_once __DIR__ . '/_auth_user.php';
?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Forms - Police Portal Pro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@3.3.0/dist/tailwind.min.css" rel="stylesheet">
</head>
<body>
    <div class="container my-5">
        <h1 class="mb-4">My Forms <span class="badge bg-primary">Officer</span></h1>
        <?php
        require_once __DIR__ . '/../config/db.php';
        // List all forms
        $forms = $pdo->query('SELECT id, name, `schema` FROM FORM ORDER BY name')->fetchAll();
        $selected_form = null;
        $fields = [];
        $success = false;
        if (isset($_GET['form_id'])) {
            foreach ($forms as $f) {
                if ($f['id'] == (int)$_GET['form_id']) {
                    $selected_form = $f;
                    $fields = json_decode($f['schema'], true)['fields'] ?? [];
                    break;
                }
            }
        }
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['form_id'])) {
            $form_id = (int)$_POST['form_id'];
            $data = [];
            $form_fields = json_decode($pdo->query('SELECT `schema` FROM FORM WHERE id=' . $form_id)->fetchColumn(), true)['fields'] ?? [];
            foreach ($form_fields as $fld) {
                $key = $fld['name'];
                $val = $_POST['field_' . md5($key)] ?? null;
                if (($fld['required'] ?? false) && ($val === null || $val === '')) {
                    $error = 'Field "' . htmlspecialchars($key) . '" is required.';
                    break;
                }
                $data[$key] = $val;
            }
            if (!isset($error)) {
                $stmt = $pdo->prepare('INSERT INTO FORM_INSTANCE (form_id, officer_id, data, submitted_at) VALUES (?, ?, ?, NOW())');
                $stmt->execute([$form_id, $_SESSION['badge_id'], json_encode($data)]);
                $success = true;
            }
        }
        ?>
        <?php if ($success): ?>
            <div class="alert alert-success">Form submitted successfully!</div>
        <?php elseif (!empty($error)): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>
        <?php if (!$selected_form): ?>
            <h5 class="mb-3">Available Forms</h5>
            <ul class="list-group mb-4">
                <?php foreach ($forms as $f): ?>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <?= htmlspecialchars($f['name']) ?>
                        <a href="user_forms.php?form_id=<?= (int)$f['id'] ?>" class="btn btn-sm btn-primary">Fill Out</a>
                    </li>
                <?php endforeach; ?>
            </ul>
        <?php else: ?>
            <h5 class="mb-3">Fill Out: <?= htmlspecialchars($selected_form['name']) ?></h5>
            <form method="post" action="">
                <input type="hidden" name="form_id" value="<?= (int)$selected_form['id'] ?>">
                <?php foreach ($fields as $fld): ?>
                    <div class="mb-3">
                        <label class="form-label"><b><?= htmlspecialchars($fld['name']) ?></b><?= !empty($fld['required']) ? ' <span class=\'text-danger\'>*</span>' : '' ?></label>
                        <?php if ($fld['type'] === 'text'): ?>
                            <input type="text" class="form-control" name="field_<?= md5($fld['name']) ?>" <?= !empty($fld['required']) ? 'required' : '' ?>>
                        <?php elseif ($fld['type'] === 'number'): ?>
                            <input type="number" class="form-control" name="field_<?= md5($fld['name']) ?>" <?= !empty($fld['required']) ? 'required' : '' ?>>
                        <?php elseif ($fld['type'] === 'date'): ?>
                            <input type="date" class="form-control" name="field_<?= md5($fld['name']) ?>" <?= !empty($fld['required']) ? 'required' : '' ?>>
                        <?php elseif ($fld['type'] === 'select'): ?>
                            <select class="form-select" name="field_<?= md5($fld['name']) ?>" <?= !empty($fld['required']) ? 'required' : '' ?>>
                                <option value="">-- Select --</option>
                                <?php foreach (explode(',', $fld['options'] ?? '') as $opt): $opt = trim($opt); if ($opt): ?>
                                    <option value="<?= htmlspecialchars($opt) ?>"><?= htmlspecialchars($opt) ?></option>
                                <?php endif; endforeach; ?>
                            </select>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
                <button type="submit" class="btn btn-success">Submit</button>
                <a href="user_forms.php" class="btn btn-secondary ms-2">Cancel</a>
            </form>
        <?php endif; ?>
        <a href="/dashboard.php" class="btn btn-secondary mt-4">Back to Dashboard</a>
    </div>
</body>
</html>
