<?php
// utils/SecureUpload.php

namespace Utils;

use phpseclib3\Crypt\AES;
use phpseclib3\Crypt\Random;

class SecureUpload {
    private $uploadDir;
    private $encryptionKey;

    public function __construct($uploadDir, $encryptionKey) {
        $this->uploadDir = rtrim($uploadDir, '/') . '/';
        $this->encryptionKey = $encryptionKey;

        // Ensure upload directory exists and is writable
        if (!is_dir($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
        }
        if (!is_writable($this->uploadDir)) {
            throw new \Exception("Upload directory is not writable: " . $this->uploadDir);
        }
    }

    /**
     * Uploads a file, encrypts it, and stores it.
     * @param array $file The $_FILES array entry for the uploaded file.
     * @return array Status and details of the upload.
     */
    public function uploadFile(array $file): array {
        if (!isset($file['error']) || is_array($file['error'])) {
            return ['status' => 'error', 'message' => 'Invalid parameters or multiple file uploads not supported.'];
        }

        switch ($file['error']) {
            case UPLOAD_ERR_OK:
                break; // Execution continues past the switch
            case UPLOAD_ERR_NO_FILE:
                return ['status' => 'error', 'message' => 'No file sent.'];
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                return ['status' => 'error', 'message' => 'Exceeded filesize limit.'];
            default:
                return ['status' => 'error', 'message' => 'Unknown upload error.'];
        }

        // Validate file size (e.g., max 25MB as per Prompt.md)
        $maxFileSize = 25 * 1024 * 1024; // 25 MB
        if ($file['size'] > $maxFileSize) {
            return ['status' => 'error', 'message' => 'File size exceeds 25MB limit.'];
        }

        // Get file info
        $originalFileName = basename($file['name']);
        $fileExtension = pathinfo($originalFileName, PATHINFO_EXTENSION);
        $mimeType = mime_content_type($file['tmp_name']);

        // Generate a unique filename to store (UUID to prevent collisions)
        $uniqueFileName = uniqid() . '_' . $this->generateRandomString(10) . '.' . $fileExtension . '.enc';
        $destinationPath = $this->uploadDir . $uniqueFileName;

        // Read file content
        $fileContent = file_get_contents($file['tmp_name']);
        if ($fileContent === false) {
            return ['status' => 'error', 'message' => 'Failed to read uploaded file content.'];
        }

        // Encrypt the file content
        try {
            $cipher = new AES('ctr');
            $cipher->setKey(hex2bin($this->encryptionKey));

            // Generate a random IV for each encryption operation
            $iv = Random::string(16); // AES uses a 16-byte IV
            $cipher->setIV($iv);

            $encryptedContent = $cipher->encrypt($fileContent);

            // Prepend IV to the encrypted content for decryption later
            $storedContent = $iv . $encryptedContent;

            // Save the encrypted file
            if (!file_put_contents($destinationPath, $storedContent)) {
                return ['status' => 'error', 'message' => 'Failed to write encrypted file to disk.'];
            }

            // Remove temporary file
            unlink($file['tmp_name']);

            return [
                'status' => 'success',
                'originalFileName' => $originalFileName,
                'storedPath' => $destinationPath,
                'fileSize' => $file['size'],
                'mimeType' => $mimeType
            ];
        } catch (\Exception $e) {
            error_log("Encryption error: " . $e->getMessage());
            return ['status' => 'error', 'message' => 'Encryption failed: ' . $e->getMessage()];
        }

        // Defensive return: This should ideally not be reached if all logic paths are covered.
        // But it prevents TypeError if some unforeseen condition leads to no explicit return.
        return ['status' => 'error', 'message' => 'An unexpected error occurred during upload.'];
    }

    /**
     * Decrypts and returns the content of a stored file.
     * @param string $filePath The path to the encrypted file.
     * @return string|false Decrypted content or false on failure.
     */
    public function decryptFile(string $filePath) {
        if (!file_exists($filePath)) {
            return false;
        }

        $storedContent = file_get_contents($filePath);
        if ($storedContent === false) {
            return false;
        }

        // Extract IV (first 16 bytes) and encrypted content
        $iv = substr($storedContent, 0, 16);
        $encryptedContent = substr($storedContent, 16);

        try {
            $cipher = new AES('ctr'); // Counter mode
            $cipher->setKey(hex2bin($this->encryptionKey));
            $cipher->setIV($iv); // Set the same IV used during encryption

            $decryptedContent = $cipher->decrypt($encryptedContent);
            return $decryptedContent;
        } catch (\Exception $e) {
            error_log("Decryption error for file $filePath: " . $e->getMessage());
            return false;
        }
    }

    // Helper for generating random strings for unique filenames
    private function generateRandomString($length = 10) {
        return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
    }
}