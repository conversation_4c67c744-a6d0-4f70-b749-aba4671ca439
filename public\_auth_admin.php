<?php
declare(strict_types=1);
session_set_cookie_params([
    'lifetime' => 90000,
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Strict',
]);
session_start();
if (!isset($_SESSION['badge_id']) || !isset($_SESSION['permissions']) || !in_array('EDIT_PERMISSIONS', $_SESSION['permissions'], true)) {
    session_unset();
    session_destroy();
    header('Location: /index.php');
    exit;
}
