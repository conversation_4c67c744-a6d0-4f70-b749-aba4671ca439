-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 08, 2025 at 10:59 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `police_portal_3`
--

-- --------------------------------------------------------

--
-- Table structure for table `audit_log`
--

CREATE TABLE `audit_log` (
  `id` char(36) NOT NULL,
  `officer_id` varchar(32) NOT NULL,
  `action` varchar(100) NOT NULL,
  `target` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`target`)),
  `timestamp` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `audit_log`
--

INSERT INTO `audit_log` (`id`, `officer_id`, `action`, `target`, `timestamp`) VALUES
('013a69fa-44ab-11f0-9098-9c5a44199649', 'C3003', 'LOGIN_SUCCESS', '{\"ip\":\"::1\"}', '2025-06-08 23:55:38'),
('01ccdd8f-44ab-11f0-9098-9c5a44199649', 'B2002', 'LOGIN_SUCCESS', '{\"ip\":\"::1\"}', '2025-06-08 23:55:39'),
('0f9e69ca-448e-11f0-9098-9c5a44199649', 'C3003', 'LOGIN_SUCCESS', '{\"ip\":\"::1\"}', '2025-06-08 20:28:27'),
('152f89e7-44ab-11f0-9098-9c5a44199649', 'A1001', 'LOGIN_SUCCESS', '{\"ip\":\"::1\"}', '2025-06-08 23:56:12'),
('17fa79c6-448c-11f0-9098-9c5a44199649', 'C3003', 'LOGIN_SUCCESS', '{\"ip\":\"::1\"}', '2025-06-08 20:14:22'),
('53fa109d-44a4-11f0-9098-9c5a44199649', 'B2002', 'LOGIN_SUCCESS', '{\"ip\":\"::1\"}', '2025-06-08 23:07:50'),
('561c6123-4483-11f0-9098-9c5a44199649', 'C3003', 'LOGIN_SUCCESS', '{\"ip\":\"::1\"}', '2025-06-08 19:11:44'),
('565ac655-44a4-11f0-9098-9c5a44199649', 'C3003', 'LOGIN_SUCCESS', '{\"ip\":\"::1\"}', '2025-06-08 23:07:54'),
('5e5d00cf-4490-11f0-9098-9c5a44199649', 'C3003', 'LOGIN_SUCCESS', '{\"ip\":\"::1\"}', '2025-06-08 20:44:58'),
('8cbeee06-449b-11f0-9098-9c5a44199649', 'B2002', 'LOGIN_SUCCESS', '{\"ip\":\"::1\"}', '2025-06-08 22:05:00'),
('9132682e-44a0-11f0-9098-9c5a44199649', 'B2002', 'LOGIN_SUCCESS', '{\"ip\":\"::1\"}', '2025-06-08 22:40:55'),
('9b66027f-448d-11f0-9098-9c5a44199649', 'C3003', 'LOGIN_SUCCESS', '{\"ip\":\"::1\"}', '2025-06-08 20:25:12'),
('a307bd3c-449a-11f0-9098-9c5a44199649', 'C3003', 'LOGIN_SUCCESS', '{\"ip\":\"::1\"}', '2025-06-08 21:58:28'),
('c4b9e83b-449c-11f0-9098-9c5a44199649', 'C3003', 'LOGIN_SUCCESS', '{\"ip\":\"::1\"}', '2025-06-08 22:13:44'),
('cd303b9a-44a7-11f0-9098-9c5a44199649', 'B2002', 'LOGIN_SUCCESS', '{\"ip\":\"::1\"}', '2025-06-08 23:32:42'),
('d5c9484e-44a7-11f0-9098-9c5a44199649', 'C3003', 'LOGIN_SUCCESS', '{\"ip\":\"::1\"}', '2025-06-08 23:32:57'),
('dd8ad4e4-449d-11f0-9098-9c5a44199649', 'B2002', 'LOGIN_SUCCESS', '{\"ip\":\"::1\"}', '2025-06-08 22:21:35'),
('e5c94484-4481-11f0-9098-9c5a44199649', 'C3003', 'LOGIN_SUCCESS', '{\"ip\":\"::1\"}', '2025-06-08 19:01:26'),
('f97956bb-449e-11f0-9098-9c5a44199649', 'C3003', 'LOGIN_SUCCESS', '{\"ip\":\"::1\"}', '2025-06-08 22:29:31');

-- --------------------------------------------------------

--
-- Table structure for table `chat_channel`
--

CREATE TABLE `chat_channel` (
  `id` char(36) NOT NULL,
  `name` varchar(64) NOT NULL,
  `is_private` tinyint(1) NOT NULL DEFAULT 0,
  `description` text DEFAULT NULL,
  `created_by` varchar(32) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `chat_channel`
--

INSERT INTO `chat_channel` (`id`, `name`, `is_private`, `description`, `created_by`, `created_at`) VALUES
('72ccf5ba-449a-11f0-9098-9c5a44199649', 'General', 0, 'General discussions for all officers.', 'DUMMY_OFFICER_ID_123', '2025-06-08 21:57:07'),
('72cd0073-449a-11f0-9098-9c5a44199649', 'Operations', 0, 'Discussions related to ongoing police operations.', 'DUMMY_OFFICER_ID_123', '2025-06-08 21:57:07'),
('72cd0199-449a-11f0-9098-9c5a44199649', 'Investigations', 0, 'Dedicated channel for ongoing investigations.', 'DUMMY_OFFICER_ID_123', '2025-06-08 21:57:07');

-- --------------------------------------------------------

--
-- Table structure for table `chat_channel_member`
--

CREATE TABLE `chat_channel_member` (
  `channel_id` char(36) NOT NULL,
  `officer_id` varchar(32) NOT NULL,
  `joined_at` datetime NOT NULL DEFAULT current_timestamp(),
  `invited_by` varchar(32) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `chat_message`
--

CREATE TABLE `chat_message` (
  `id` char(36) NOT NULL,
  `channel_id` char(36) NOT NULL,
  `sender_id` varchar(255) NOT NULL,
  `content` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `file_path` varchar(512) DEFAULT NULL,
  `file_name` varchar(255) DEFAULT NULL,
  `file_size` int(11) DEFAULT NULL,
  `file_type` varchar(128) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `chat_message`
--

INSERT INTO `chat_message` (`id`, `channel_id`, `sender_id`, `content`, `created_at`, `file_path`, `file_name`, `file_size`, `file_type`) VALUES
('05683304-fa5a-494d-2bcc-5574270976b2', '72ccf5ba-449a-11f0-9098-9c5a44199649', 'B2002', 'hello', '2025-06-08 23:55:55', NULL, NULL, NULL, NULL),
('06e16e2d-4b50-6d51-03bd-ebd81992b1b4', '72ccf5ba-449a-11f0-9098-9c5a44199649', 'B2002', 'hi', '2025-06-08 23:42:16', NULL, NULL, NULL, NULL),
('151960a7-055d-5e02-5593-9722d6a539c9', '72ccf5ba-449a-11f0-9098-9c5a44199649', 'B2002', 'hi', '2025-06-08 23:38:54', NULL, NULL, NULL, NULL),
('1cb0aeab-0f27-8fe4-044d-4039bc012f32', '72cd0199-449a-11f0-9098-9c5a44199649', 'C3003', 'hello2', '2025-06-08 23:56:44', NULL, NULL, NULL, NULL),
('c84c096d-750e-d593-1e70-affd0a6e9cdf', '72cd0199-449a-11f0-9098-9c5a44199649', 'B2002', 'hello 1', '2025-06-08 23:56:37', NULL, NULL, NULL, NULL),
('e600a917-7535-4b81-bebc-b067925290b4', '72ccf5ba-449a-11f0-9098-9c5a44199649', 'A1001', 'sup', '2025-06-08 23:56:23', NULL, NULL, NULL, NULL),
('e8d2f29a-19b6-f641-3b19-0d66d420c94a', '72ccf5ba-449a-11f0-9098-9c5a44199649', 'B2002', 'hi', '2025-06-08 23:34:00', NULL, NULL, NULL, NULL),
('ffda4bcf-f4d9-a476-de56-2d199f7f4520', '72ccf5ba-449a-11f0-9098-9c5a44199649', 'C3003', 'hi', '2025-06-08 23:55:49', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `chat_pinned`
--

CREATE TABLE `chat_pinned` (
  `channel_id` char(36) NOT NULL,
  `message_id` char(36) NOT NULL,
  `pinned_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `form`
--

CREATE TABLE `form` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `schema` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`schema`)),
  `version` int(11) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `form_instance`
--

CREATE TABLE `form_instance` (
  `id` int(11) NOT NULL,
  `form_id` int(11) NOT NULL,
  `officer_id` varchar(32) NOT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`data`)),
  `submitted_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `form_version`
--

CREATE TABLE `form_version` (
  `id` int(11) NOT NULL,
  `form_id` int(11) NOT NULL,
  `schema` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`schema`)),
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `officer`
--

CREATE TABLE `officer` (
  `badge_id` varchar(32) NOT NULL,
  `name` varchar(100) NOT NULL,
  `rank` varchar(50) NOT NULL,
  `assigned_zone` varchar(64) DEFAULT NULL,
  `permissions` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`permissions`)),
  `password_hash` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `officer`
--

INSERT INTO `officer` (`badge_id`, `name`, `rank`, `assigned_zone`, `permissions`, `password_hash`, `created_at`) VALUES
('A1001', 'Alice Admin', 'Chief', 'Zone 1', '[\"EDIT_PERMISSIONS\",\"MANAGE_FORMS\",\"VIEW_AUDIT_LOG\",\"CHAT_ADMIN\",\"CHAT_SEND\"]', '$2y$10$qdqKlejiAo4XwahWU82PXOXNg6CO0TcoCqS6sZfos5Su1YfG7GJHa', '2025-06-08 18:57:05'),
('B2002', 'Bob Officer', 'Sergeant', 'Zone 2', '[\"CHAT_SEND\"]', '$2y$10$y.2iHb2sg3gH3XOiZV0g0ehmtImrlA/HYXWJDB6RVsKCAQeU3vSKy', '2025-06-08 18:57:05'),
('C3003', 'Carol User', 'Constable', 'Zone 3', '[\"CHAT_SEND\"]', '$2y$10$VM/TPKVhcGOQ92IS.40/nuA8vxBMCBo7vFSB4FEHQsDlcz5KeslC2', '2025-06-08 18:57:05');

-- --------------------------------------------------------

--
-- Table structure for table `officer_permission`
--

CREATE TABLE `officer_permission` (
  `officer_id` varchar(32) NOT NULL,
  `permission_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `officer_permission`
--

INSERT INTO `officer_permission` (`officer_id`, `permission_id`) VALUES
('A1001', 1),
('A1001', 2),
('A1001', 3),
('A1001', 4),
('A1001', 5),
('A1001', 6),
('B2002', 5),
('C3003', 5);

-- --------------------------------------------------------

--
-- Table structure for table `online_user`
--

CREATE TABLE `online_user` (
  `officer_id` varchar(32) NOT NULL,
  `last_seen` datetime NOT NULL,
  `is_online` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `permission`
--

CREATE TABLE `permission` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `permission`
--

INSERT INTO `permission` (`id`, `name`, `description`) VALUES
(1, 'VIEW_DASHBOARD', 'Access dashboard'),
(2, 'EDIT_PERMISSIONS', 'Modify officer permissions'),
(3, 'MANAGE_FORMS', 'Create/edit forms'),
(4, 'VIEW_AUDIT_LOG', 'See audit logs'),
(5, 'CHAT_SEND', 'Send chat messages'),
(6, 'CHAT_ADMIN', 'Manage chat channels');

-- --------------------------------------------------------

--
-- Table structure for table `station`
--

CREATE TABLE `station` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `type` enum('Division','Department','District','Zone') NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `location` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`location`))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `station`
--

INSERT INTO `station` (`id`, `name`, `type`, `parent_id`, `location`) VALUES
(1, 'Central Division', 'Division', NULL, '{\"lat\":40.1,\"lng\":-74.1}'),
(2, 'Operations Dept', 'Department', 1, '{\"lat\":40.2,\"lng\":-74.2}'),
(3, 'North District', 'District', 2, '{\"lat\":40.3,\"lng\":-74.3}'),
(4, 'Zone 1', 'Zone', 3, '{\"lat\":40.4,\"lng\":-74.4}'),
(5, 'Zone 2', 'Zone', 3, '{\"lat\":40.5,\"lng\":-74.5}');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `audit_log`
--
ALTER TABLE `audit_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `officer_id` (`officer_id`);

--
-- Indexes for table `chat_channel`
--
ALTER TABLE `chat_channel`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`);

--
-- Indexes for table `chat_channel_member`
--
ALTER TABLE `chat_channel_member`
  ADD PRIMARY KEY (`channel_id`,`officer_id`),
  ADD KEY `officer_id` (`officer_id`),
  ADD KEY `invited_by` (`invited_by`);

--
-- Indexes for table `chat_message`
--
ALTER TABLE `chat_message`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sender_id` (`sender_id`),
  ADD KEY `idx_chat_message_channel_id` (`channel_id`);

--
-- Indexes for table `chat_pinned`
--
ALTER TABLE `chat_pinned`
  ADD PRIMARY KEY (`channel_id`,`message_id`),
  ADD KEY `message_id` (`message_id`);

--
-- Indexes for table `form`
--
ALTER TABLE `form`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `form_instance`
--
ALTER TABLE `form_instance`
  ADD PRIMARY KEY (`id`),
  ADD KEY `form_id` (`form_id`),
  ADD KEY `officer_id` (`officer_id`);

--
-- Indexes for table `form_version`
--
ALTER TABLE `form_version`
  ADD PRIMARY KEY (`id`),
  ADD KEY `form_id` (`form_id`);

--
-- Indexes for table `officer`
--
ALTER TABLE `officer`
  ADD PRIMARY KEY (`badge_id`);

--
-- Indexes for table `officer_permission`
--
ALTER TABLE `officer_permission`
  ADD PRIMARY KEY (`officer_id`,`permission_id`),
  ADD KEY `permission_id` (`permission_id`);

--
-- Indexes for table `online_user`
--
ALTER TABLE `online_user`
  ADD PRIMARY KEY (`officer_id`);

--
-- Indexes for table `permission`
--
ALTER TABLE `permission`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `station`
--
ALTER TABLE `station`
  ADD PRIMARY KEY (`id`),
  ADD KEY `parent_id` (`parent_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `form`
--
ALTER TABLE `form`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `form_instance`
--
ALTER TABLE `form_instance`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `form_version`
--
ALTER TABLE `form_version`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `permission`
--
ALTER TABLE `permission`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `station`
--
ALTER TABLE `station`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `audit_log`
--
ALTER TABLE `audit_log`
  ADD CONSTRAINT `audit_log_ibfk_1` FOREIGN KEY (`officer_id`) REFERENCES `officer` (`badge_id`) ON DELETE CASCADE;

--
-- Constraints for table `chat_channel`
--
ALTER TABLE `chat_channel`
  ADD CONSTRAINT `chat_channel_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `officer` (`badge_id`);

--
-- Constraints for table `chat_channel_member`
--
ALTER TABLE `chat_channel_member`
  ADD CONSTRAINT `chat_channel_member_ibfk_1` FOREIGN KEY (`channel_id`) REFERENCES `chat_channel` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `chat_channel_member_ibfk_2` FOREIGN KEY (`officer_id`) REFERENCES `officer` (`badge_id`),
  ADD CONSTRAINT `chat_channel_member_ibfk_3` FOREIGN KEY (`invited_by`) REFERENCES `officer` (`badge_id`);

--
-- Constraints for table `chat_message`
--
ALTER TABLE `chat_message`
  ADD CONSTRAINT `chat_message_ibfk_1` FOREIGN KEY (`channel_id`) REFERENCES `chat_channel` (`id`),
  ADD CONSTRAINT `chat_message_ibfk_2` FOREIGN KEY (`sender_id`) REFERENCES `officer` (`badge_id`);

--
-- Constraints for table `chat_pinned`
--
ALTER TABLE `chat_pinned`
  ADD CONSTRAINT `chat_pinned_ibfk_1` FOREIGN KEY (`channel_id`) REFERENCES `chat_channel` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `chat_pinned_ibfk_2` FOREIGN KEY (`message_id`) REFERENCES `chat_message` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `form_instance`
--
ALTER TABLE `form_instance`
  ADD CONSTRAINT `form_instance_ibfk_1` FOREIGN KEY (`form_id`) REFERENCES `form` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `form_instance_ibfk_2` FOREIGN KEY (`officer_id`) REFERENCES `officer` (`badge_id`) ON DELETE CASCADE;

--
-- Constraints for table `form_version`
--
ALTER TABLE `form_version`
  ADD CONSTRAINT `form_version_ibfk_1` FOREIGN KEY (`form_id`) REFERENCES `form` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `officer_permission`
--
ALTER TABLE `officer_permission`
  ADD CONSTRAINT `officer_permission_ibfk_1` FOREIGN KEY (`officer_id`) REFERENCES `officer` (`badge_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `officer_permission_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permission` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `online_user`
--
ALTER TABLE `online_user`
  ADD CONSTRAINT `online_user_ibfk_1` FOREIGN KEY (`officer_id`) REFERENCES `officer` (`badge_id`);

--
-- Constraints for table `station`
--
ALTER TABLE `station`
  ADD CONSTRAINT `station_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `station` (`id`) ON DELETE SET NULL;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
