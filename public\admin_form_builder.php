<?php
// Admin: Form Builder (placeholder)
declare(strict_types=1);
require_once __DIR__ . '/_auth_admin.php';
?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Builder - Police Portal Pro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@3.3.0/dist/tailwind.min.css" rel="stylesheet">
</head>
<body>
    <div class="container my-5">
        <h1 class="mb-4">Form Builder <span class="badge bg-warning text-dark">Admin</span></h1>
        <?php
        require_once __DIR__ . '/../config/db.php';
        require_once __DIR__ . '/_auth_admin.php';
        if (session_status() !== PHP_SESSION_ACTIVE) session_start();
        $message = null;
        $is_error = false;
        // Handle create form POST
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['formName'], $_POST['formSchema'])) {
            $name = trim($_POST['formName']);
            $schema = trim($_POST['formSchema']);
            if ($name === '' || strlen($name) > 100) {
                $is_error = true;
                $message = 'Form name is required and must be less than 100 characters.';
            } else {
                $json = json_decode($schema, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $is_error = true;
                    $message = 'Invalid JSON schema.';
                } else {
                    try {
                        $pdo->beginTransaction();
                        $stmt = $pdo->prepare('INSERT INTO FORM (name, `schema`, version) VALUES (?, ?, 1)');
                        $stmt->execute([$name, $schema]);
                        $form_id = $pdo->lastInsertId();
                        // Audit log
                        $pdo->prepare('INSERT INTO AUDIT_LOG (id, officer_id, action, target, timestamp) VALUES (UUID(), ?, ?, ?, NOW())')
                            ->execute([
                                $_SESSION['badge_id'], 'FORM_CREATE', json_encode(['form_id'=>$form_id, 'name'=>$name])
                            ]);
                        $pdo->commit();
                        $message = 'Form "' . htmlspecialchars($name) . '" created successfully!';
                    } catch (Throwable $e) {
                        $pdo->rollBack();
                        $is_error = true;
                        $message = 'Error creating form: ' . $e->getMessage();
                    }
                }
            }
        }
        // Fetch all forms
        $forms = $pdo->query('SELECT id, name, version FROM FORM ORDER BY name')->fetchAll();
        ?>
        <?php if ($message): ?>
            <div class="alert <?= $is_error ? 'alert-danger' : 'alert-success' ?>"> <?= $message ?> </div>
        <?php endif; ?>
        <div class="mb-4 d-flex justify-content-between align-items-center">
            <h5 class="mb-0">All Forms</h5>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createFormModal">Create New Form</button>
        </div>
        <table class="table table-bordered">
            <thead class="table-light">
                <tr>
                    <th>Name</th>
                    <th>Version</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($forms as $form): ?>
                <tr>
                    <td><?= htmlspecialchars($form['name']) ?></td>
                    <td><?= (int)$form['version'] ?></td>
                    <td>
                        <a href="admin_form_edit.php?id=<?= (int)$form['id'] ?>" class="btn btn-sm btn-secondary">Edit</a>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <!-- Create Form Modal -->
        <div class="modal fade" id="createFormModal" tabindex="-1" aria-labelledby="createFormModalLabel" aria-hidden="true">
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="createFormModalLabel">Create New Form</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
              </div>
              <div class="modal-body">
                <form method="post" action="" id="visualFormBuilder">
                  <div class="mb-3">
                    <label for="formName" class="form-label">Form Name</label>
                    <input type="text" class="form-control" id="formName" name="formName" required maxlength="100">
                  </div>
                  <div class="mb-2"><b>Fields</b></div>
                  <div id="fieldsContainer"></div>
                  <button type="button" class="btn btn-outline-primary btn-sm mb-3" id="addFieldBtn">+ Add Field</button>
                  <input type="hidden" name="formSchema" id="formSchema">
                  <div class="mb-3 mt-2">
                    <label class="form-label">Live Preview</label>
                    <ul class="list-group" id="fieldsPreview"></ul>
                  </div>
                  <button type="submit" class="btn btn-primary">Save</button>
                </form>
                <script>
                let fields = [];
                function renderFields() {
                  const container = document.getElementById('fieldsContainer');
                  const preview = document.getElementById('fieldsPreview');
                  container.innerHTML = '';
                  preview.innerHTML = '';
                  fields.forEach((f, i) => {
                    const row = document.createElement('div');
                    row.className = 'row mb-2 align-items-center';
                    row.innerHTML = `
                      <div class="col-3"><input type="text" class="form-control" placeholder="Field Name" value="${f.name||''}" onchange="fields[${i}].name=this.value;renderFields()" required></div>
                      <div class="col-2">
                        <select class="form-select" onchange="fields[${i}].type=this.value;renderFields()">
                          <option value="text" ${f.type==='text'?'selected':''}>Text</option>
                          <option value="number" ${f.type==='number'?'selected':''}>Number</option>
                          <option value="date" ${f.type==='date'?'selected':''}>Date</option>
                          <option value="select" ${f.type==='select'?'selected':''}>Select</option>
                        </select>
                      </div>
                      <div class="col-2"><input type="checkbox" ${f.required?'checked':''} onchange="fields[${i}].required=this.checked;renderFields()"> Required</div>
                      <div class="col-4">${f.type==='select'?`<input type="text" class="form-control" style="min-width:180px;" placeholder="Option1, Option2, Option3" value="${f.options||''}" onchange="fields[${i}].options=this.value;renderFields()"><small class='text-muted'>Comma separated</small>`:''}</div>
                      <div class="col-2">
                        <button type="button" class="btn btn-secondary btn-sm" onclick="if(${i}>0){[fields[${i}-1],fields[${i}]]=[fields[${i}],fields[${i}-1]];renderFields()}"">↑</button>
                        <button type="button" class="btn btn-secondary btn-sm" onclick="if(${i}<fields.length-1){[fields[${i}+1],fields[${i}]]=[fields[${i}],fields[${i}+1]];renderFields()}"">↓</button>
                        <button type="button" class="btn btn-danger btn-sm" onclick="fields.splice(${i},1);renderFields()">Remove</button>
                      </div>
                    `;
                    container.appendChild(row);
                    const li = document.createElement('li');
                    li.className = 'list-group-item';
                    let desc = f.name + ' (' + f.type + ')';
                    if (f.required) desc += ' [required]';
                    if (f.type==='select' && f.options) desc += ' ['+f.options+']';
                    li.innerText = desc;
                    preview.appendChild(li);
                  });
                  document.getElementById('formSchema').value = JSON.stringify({fields: fields});
                }
                document.getElementById('addFieldBtn').onclick = function() {
                  fields.push({name:'',type:'text',required:false,options:''}); renderFields();
                };
                document.getElementById('visualFormBuilder').onsubmit = function(e) {
                  renderFields();
                };
                renderFields();
                </script>
              </div>
            </div>
          </div>
        </div>
        <a href="/dashboard.php" class="btn btn-secondary mt-4">Back to Dashboard</a>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
