<?php
// public/chat_api.php
// Ensure all errors are displayed for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

require dirname(__DIR__) . '/vendor/autoload.php';
require dirname(__DIR__) . '/config/config.php'; // Include your config file

use Config\Database;
use Utils\SecureUpload;

// Check for authenticated user (e.g., via _auth_user.php)
require_once dirname(__DIR__) . '/public/_auth_user.php'; // Enforce authentication and session

// Using $_SESSION['badge_id'] as the sender ID for chat
$senderId = $_SESSION['badge_id'] ?? 'DUMMY_OFFICER_ID_123'; // Fallback for temporary testing

$pdo = Database::getConnection();

// --- DEBUG LOGGING START ---
error_log("API Request - Method: " . $_SERVER['REQUEST_METHOD']);
error_log("API Request - GET Params: " . print_r($_GET, true));
// --- DEBUG LOGGING END ---


// Handle POST requests for media upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['chat_media'])) {
    error_log("API: Handling media upload POST request.");
    $uploadDir = UPLOAD_BASE_DIR . 'chat/';
    $secureUpload = new SecureUpload($uploadDir, SECURE_UPLOAD_KEY);

    $uploadResult = $secureUpload->uploadFile($_FILES['chat_media']);

    if ($uploadResult['status'] === 'success') {
        try {
            $channelId = $_POST['channel_id'] ?? null;
            if (!$channelId) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => 'Channel ID is required for media upload.']);
                exit;
            }

            $messageId = vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex(random_bytes(16)), 4));

            $stmt = $pdo->prepare("INSERT INTO CHAT_MESSAGE (id, sender_id, content, created_at, file_path, file_name, file_size, file_type, channel_id) VALUES (:id, :sender_id, :content, NOW(), :file_path, :file_name, :file_size, :file_type, :channel_id)");

            $stmt->execute([
                ':id' => $messageId,
                ':sender_id' => $senderId,
                ':content' => '[MEDIA_ATTACHMENT]',
                ':file_path' => $uploadResult['storedPath'],
                ':file_name' => $uploadResult['originalFileName'],
                ':file_size' => $uploadResult['fileSize'],
                ':file_type' => $uploadResult['mimeType'],
                ':channel_id' => $channelId
            ]);

            echo json_encode([
                'status' => 'success',
                'message' => 'File uploaded and chat message recorded.',
                'chatMessageId' => $messageId,
                'fileDetails' => $uploadResult,
                'sender_id' => $senderId,
                'channel_id' => $channelId
            ]);

        } catch (\PDOException $e) {
            http_response_code(500);
            echo json_encode(['status' => 'error', 'message' => 'Database error recording media message: ' . $e->getMessage()]);
            if (file_exists($uploadResult['storedPath'])) {
                unlink($uploadResult['storedPath']);
            }
        }
    } else {
        http_response_code(400);
        echo json_encode($uploadResult);
    }
    exit;
}

// Handle GET requests for chat history
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action']) && $_GET['action'] === 'get_history') {
    error_log("API: Handling get_history GET request.");
    $channelId = $_GET['channel_id'] ?? null;
    $limit = (int) ($_GET['limit'] ?? 50);
    $offset = (int) ($_GET['offset'] ?? 0);

    if (!$channelId) {
        http_response_code(400);
        echo json_encode(['status' => 'error', 'message' => 'Channel ID is required to fetch history.']);
        exit;
    }

    try {
        $sql = "SELECT cm.id, cm.sender_id, o.name AS officer_name, cm.content, cm.created_at, cm.file_path, cm.file_name, cm.file_size, cm.file_type, cm.channel_id
                FROM CHAT_MESSAGE cm
                JOIN OFFICER o ON cm.sender_id = o.badge_id
                WHERE cm.channel_id = :channel_id";
        $params = [':channel_id' => $channelId];

        $sql .= " ORDER BY cm.created_at DESC LIMIT :limit OFFSET :offset";

        $stmt = $pdo->prepare($sql);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT); // Corrected this line
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();
        $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $messages = array_reverse($messages);

        echo json_encode(['status' => 'success', 'messages' => $messages]);

    } catch (\PDOException $e) {
        http_response_code(500);
        echo json_encode(['status' => 'error', 'message' => 'Database error fetching history: ' . $e->getMessage()]);
    }
    exit;
}

// Handle GET requests to list channels
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action']) && $_GET['action'] === 'get_channels') {
    error_log("API: Handling get_channels GET request.");
    try {
        $stmt = $pdo->query("SELECT id, name, description FROM CHAT_CHANNEL ORDER BY name ASC");
        $channels = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo json_encode(['status' => 'success', 'channels' => $channels]);
    } catch (\PDOException $e) {
        http_response_code(500);
        echo json_encode(['status' => 'error', 'message' => 'Database error fetching channels: ' . $e->getMessage()]);
    }
    exit;
}

// Handle GET requests to list officers (users)
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action']) && $_GET['action'] === 'get_officers') {
    error_log("API: Handling get_officers GET request.");
    try {
        $stmt = $pdo->query("SELECT badge_id, name FROM OFFICER ORDER BY name ASC");
        $officers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo json_encode(['status' => 'success', 'officers' => $officers]);
    } catch (\PDOException $e) {
        http_response_code(500);
        echo json_encode(['status' => 'error', 'message' => 'Database error fetching officers: ' . $e->getMessage()]);
    }
    exit;
}

// Handle GET requests to serve media files securely
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action']) && $_GET['action'] === 'get_media' && isset($_GET['message_id'])) {
    error_log("API: Handling get_media GET request.");
    $messageId = $_GET['message_id'];

    try {
        $stmt = $pdo->prepare("SELECT file_path, file_name, file_type FROM CHAT_MESSAGE WHERE id = :message_id");
        $stmt->execute([':message_id' => $messageId]);
        $message = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($message && !empty($message['file_path'])) {
            $filePath = $message['file_path'];

            $secureUpload = new SecureUpload(UPLOAD_BASE_DIR . 'chat/', SECURE_UPLOAD_KEY);

            $decryptedContent = $secureUpload->decryptFile($filePath);

            if ($decryptedContent !== false) {
                header('Content-Type: ' . ($message['file_type'] ?? 'application/octet-stream'));
                header('Content-Disposition: inline; filename="' . ($message['file_name'] ?? 'download') . '"');
                header('Content-Length: ' . strlen($decryptedContent));
                echo $decryptedContent;
                exit;
            }
        }
        http_response_code(404);
        echo "File not found or access denied.";
    } catch (\Exception $e) {
        error_log("Error serving media: " . $e->getMessage());
        http_response_code(500);
        echo "Error serving file.";
    }
    exit;
}

// --- DEBUG LOGGING START ---
error_log("API: No action matched. Sending 400 Bad Request.");
// --- DEBUG LOGGING END ---

http_response_code(400); // Bad Request for unhandled requests
echo json_encode(['status' => 'error', 'message' => 'Invalid API request.']);