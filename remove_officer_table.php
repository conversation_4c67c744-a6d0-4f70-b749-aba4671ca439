<?php
/**
 * Migration Script: Remove OFFICER Table and Clean Up
 * 
 * This script safely removes the OFFICER table and any references to it,
 * ensuring the system fully uses the users table.
 */

declare(strict_types=1);
require_once __DIR__ . '/config/db.php';

echo "=== Removing OFFICER Table and Cleaning Up ===\n\n";

try {
    // Step 1: Verify users table has all necessary data
    echo "Step 1: Verifying users table integrity...\n";
    
    $stmt = $pdo->query("
        SELECT COUNT(*) as user_count,
               COUNT(CASE WHEN permission = 'admin' THEN 1 END) as admin_count,
               COUNT(CASE WHEN rank IS NOT NULL THEN 1 END) as users_with_rank,
               COUNT(CASE WHEN created_at IS NOT NULL THEN 1 END) as users_with_created_at
        FROM users
    ");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "Users table statistics:\n";
    echo "  - Total users: {$stats['user_count']}\n";
    echo "  - Admin users: {$stats['admin_count']}\n";
    echo "  - Users with rank: {$stats['users_with_rank']}\n";
    echo "  - Users with created_at: {$stats['users_with_created_at']}\n";
    
    if ($stats['user_count'] == 0) {
        throw new Exception("Users table is empty! Cannot proceed with OFFICER table removal.");
    }
    
    if ($stats['admin_count'] == 0) {
        echo "⚠️  Warning: No admin users found in users table!\n";
    }
    
    // Step 2: Check for any remaining dependencies on OFFICER table
    echo "\nStep 2: Checking for OFFICER table dependencies...\n";
    
    // Check for foreign key constraints
    $stmt = $pdo->query("
        SELECT TABLE_NAME, COLUMN_NAME, CONSTRAINT_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE REFERENCED_TABLE_NAME = 'OFFICER' OR REFERENCED_TABLE_NAME = 'officer'
    ");
    $dependencies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($dependencies)) {
        echo "Found dependencies on OFFICER table:\n";
        foreach ($dependencies as $dep) {
            echo "  - {$dep['TABLE_NAME']}.{$dep['COLUMN_NAME']} -> {$dep['REFERENCED_TABLE_NAME']}.{$dep['REFERENCED_COLUMN_NAME']}\n";
        }
    } else {
        echo "✅ No foreign key dependencies found\n";
    }
    
    // Step 3: Check for tables that might reference officer data
    echo "\nStep 3: Checking for tables with officer_id columns...\n";
    
    $tablesWithOfficerId = [];
    $stmt = $pdo->query("
        SELECT TABLE_NAME, COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE COLUMN_NAME LIKE '%officer%' 
        AND TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME != 'OFFICER'
        AND TABLE_NAME != 'officer'
    ");
    $officerColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($officerColumns)) {
        echo "Found columns that might reference officers:\n";
        foreach ($officerColumns as $col) {
            echo "  - {$col['TABLE_NAME']}.{$col['COLUMN_NAME']}\n";
            $tablesWithOfficerId[] = $col['TABLE_NAME'];
        }
        echo "\n⚠️  These tables may need manual review after OFFICER table removal\n";
    } else {
        echo "✅ No officer-related columns found in other tables\n";
    }
    
    // Step 4: Create backup of OFFICER table data (just in case)
    echo "\nStep 4: Creating backup of OFFICER table data...\n";
    
    try {
        $stmt = $pdo->query("SELECT * FROM OFFICER");
        $officerData = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($officerData)) {
            $backupFile = 'officer_table_backup_' . date('Y-m-d_H-i-s') . '.json';
            file_put_contents($backupFile, json_encode($officerData, JSON_PRETTY_PRINT));
            echo "✅ Backed up " . count($officerData) . " officer records to: $backupFile\n";
        } else {
            echo "ℹ️  OFFICER table is empty, no backup needed\n";
        }
    } catch (PDOException $e) {
        echo "ℹ️  OFFICER table doesn't exist or is inaccessible: " . $e->getMessage() . "\n";
    }
    
    // Step 5: Remove foreign key constraints that reference OFFICER table
    echo "\nStep 5: Removing foreign key constraints...\n";
    
    foreach ($dependencies as $dep) {
        try {
            $sql = "ALTER TABLE `{$dep['TABLE_NAME']}` DROP FOREIGN KEY `{$dep['CONSTRAINT_NAME']}`";
            $pdo->exec($sql);
            echo "✅ Removed foreign key constraint: {$dep['CONSTRAINT_NAME']}\n";
        } catch (PDOException $e) {
            echo "⚠️  Could not remove constraint {$dep['CONSTRAINT_NAME']}: " . $e->getMessage() . "\n";
        }
    }
    
    // Step 6: Drop OFFICER table
    echo "\nStep 6: Dropping OFFICER table...\n";
    
    try {
        $pdo->exec("DROP TABLE IF EXISTS OFFICER");
        echo "✅ Successfully dropped OFFICER table\n";
    } catch (PDOException $e) {
        echo "⚠️  Could not drop OFFICER table: " . $e->getMessage() . "\n";
    }
    
    // Also try lowercase version (in case of case sensitivity)
    try {
        $pdo->exec("DROP TABLE IF EXISTS officer");
        echo "✅ Successfully dropped officer table (lowercase)\n";
    } catch (PDOException $e) {
        echo "ℹ️  officer table (lowercase) doesn't exist or already removed\n";
    }
    
    // Step 7: Clean up any remaining officer-related tables
    echo "\nStep 7: Cleaning up related tables...\n";
    
    $relatedTables = ['officer_permission', 'OFFICER_PERMISSION'];
    foreach ($relatedTables as $table) {
        try {
            $pdo->exec("DROP TABLE IF EXISTS `$table`");
            echo "✅ Dropped table: $table\n";
        } catch (PDOException $e) {
            echo "ℹ️  Table $table doesn't exist: " . $e->getMessage() . "\n";
        }
    }
    
    // Step 8: Verify cleanup
    echo "\nStep 8: Verifying cleanup...\n";
    
    // Check if OFFICER table still exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'OFFICER'");
    if ($stmt->rowCount() == 0) {
        $stmt = $pdo->query("SHOW TABLES LIKE 'officer'");
        if ($stmt->rowCount() == 0) {
            echo "✅ OFFICER table successfully removed\n";
        } else {
            echo "⚠️  officer table (lowercase) still exists\n";
        }
    } else {
        echo "⚠️  OFFICER table still exists\n";
    }
    
    // Show current table structure
    echo "\nCurrent database tables:\n";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    foreach ($tables as $table) {
        echo "  - $table\n";
    }
    
    // Step 9: Final verification of users table
    echo "\nStep 9: Final verification of users table...\n";
    
    $stmt = $pdo->query("
        SELECT u.username, u.full_name, u.rank, u.permission, u.created_at, o.name as office_name 
        FROM users u 
        JOIN offices o ON u.office_id = o.office_id 
        ORDER BY u.permission DESC, u.rank DESC, u.full_name
    ");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Current users in system:\n";
    foreach ($users as $user) {
        $createdAt = $user['created_at'] ? date('Y-m-d H:i', strtotime($user['created_at'])) : 'Unknown';
        echo "  - {$user['username']}: {$user['full_name']} ({$user['rank']}) - {$user['permission']} at {$user['office_name']} [Created: $createdAt]\n";
    }
    
    echo "\n=== OFFICER Table Removal Completed Successfully! ===\n";
    echo "Summary:\n";
    echo "  ✅ OFFICER table and related tables removed\n";
    echo "  ✅ Foreign key constraints cleaned up\n";
    echo "  ✅ Data backed up to JSON file\n";
    echo "  ✅ Users table is fully operational\n";
    echo "  ✅ System now exclusively uses users table for authentication\n";
    
    if (!empty($tablesWithOfficerId)) {
        echo "\n⚠️  Manual Review Needed:\n";
        echo "The following tables may need manual review for officer references:\n";
        foreach (array_unique($tablesWithOfficerId) as $table) {
            echo "  - $table\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
