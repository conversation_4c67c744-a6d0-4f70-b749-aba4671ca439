<?php
// Police Portal Pro: Shared Navigation Component
// Include this file in all pages that need the persistent sidebar navigation

// Ensure session is started and user is authenticated
if (!isset($_SESSION['badge_id'])) {
    header('Location: /index.php');
    exit;
}

$badge_id = $_SESSION['badge_id'];
$permissions = $_SESSION['permissions'] ?? [];
$is_admin = in_array('EDIT_PERMISSIONS', $permissions, true);

// Get current page for active navigation - handle both direct access and dashboard routing
$current_page = basename($_SERVER['PHP_SELF']);
$page_param = $_GET['page'] ?? '';

// Determine active page for navigation highlighting
if ($current_page === 'dashboard.php' && !empty($page_param)) {
    $active_page = $page_param;
} else {
    $active_page = str_replace('.php', '', $current_page);
}

// Branding colors
$policeBlue = '#002366';
$policeGold = '#FFD700';
?>

<!-- Navigation Styles (include once per page) -->
<style>
    :root {
        --police-blue: <?= $policeBlue ?>;
        --police-gold: <?= $policeGold ?>;
        --sidebar-width: 250px;
    }

    body {
        background-color: #f8f9fa;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
        overflow-x: hidden;
    }

    /* Ensure content doesn't overlap with fixed elements */
    .police-main-content {
        padding-top: 20px;
    }

    /* Header Styles */
    .police-header {
        background: linear-gradient(135deg, var(--police-blue), #001a4d);
        border-bottom: 3px solid var(--police-gold);
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1030;
        height: 70px;
        display: flex;
        align-items: center;
        padding: 0 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .police-header .badge-icon::before {
        content: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none" stroke="%23FFD700" stroke-width="2"><circle cx="16" cy="16" r="14" stroke="%23FFD700" stroke-width="3"/><text x="16" y="22" font-size="14" text-anchor="middle" fill="%23FFD700" font-weight="bold">PD</text></svg>');
    }

    .police-header h1 {
        color: white;
        font-size: 1.5rem;
        font-weight: 700;
        letter-spacing: 2px;
        margin: 0 0 0 15px;
    }

    .police-header .user-info {
        margin-left: auto;
        color: white;
        font-weight: 500;
    }

    .hamburger {
        display: none;
        background: none;
        border: none;
        color: white;
        font-size: 1.5rem;
        margin-right: 15px;
        cursor: pointer;
    }

    /* Sidebar Styles */
    .police-sidebar {
        position: fixed;
        top: 70px;
        left: 0;
        width: var(--sidebar-width);
        height: calc(100vh - 70px);
        background: white;
        border-right: 1px solid #e9ecef;
        overflow-y: auto;
        z-index: 1020;
        transition: transform 0.3s ease;
        box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    }

    .police-sidebar.collapsed {
        transform: translateX(-100%);
    }

    .sidebar-header {
        padding: 20px;
        border-bottom: 1px solid #e9ecef;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    }

    .sidebar-nav {
        padding: 0;
        list-style: none;
        margin: 0;
    }

    .sidebar-nav .nav-item {
        border-bottom: 1px solid #f1f3f4;
    }

    .sidebar-nav .nav-link {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        color: #495057;
        text-decoration: none;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .sidebar-nav .nav-link:hover {
        background-color: #f8f9fa;
        color: var(--police-blue);
        border-left: 4px solid var(--police-blue);
        text-decoration: none;
    }

    .sidebar-nav .nav-link.active {
        background-color: var(--police-blue);
        color: white;
        border-left: 4px solid var(--police-gold);
    }

    .sidebar-nav .nav-link i {
        width: 20px;
        margin-right: 12px;
        text-align: center;
    }

    .sidebar-footer {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 20px;
        border-top: 1px solid #e9ecef;
        background: white;
    }

    /* Main Content Adjustment */
    .police-main-content {
        margin-left: var(--sidebar-width);
        margin-top: 70px;
        padding: 30px;
        min-height: calc(100vh - 70px);
        transition: margin-left 0.3s ease;
        width: calc(100% - var(--sidebar-width));
        box-sizing: border-box;
    }

    .police-main-content.expanded {
        margin-left: 0;
        width: 100%;
    }

    /* Fix for any content that might have conflicting styles */
    .police-main-content .container-fluid {
        max-width: none;
        padding-left: 15px;
        padding-right: 15px;
    }

    /* Ensure proper spacing for page headers */
    .police-main-content h1, .police-main-content h2 {
        color: var(--police-blue);
        font-weight: 600;
    }

    .police-main-content .badge {
        font-size: 0.875rem;
    }

    .role-badge {
        font-size: 0.9rem;
        padding: 8px 16px;
        border-radius: 20px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hamburger {
            display: block;
        }

        .police-sidebar {
            transform: translateX(-100%);
        }

        .police-sidebar.show {
            transform: translateX(0);
        }

        .police-main-content {
            margin-left: 0;
        }

        .police-header h1 {
            font-size: 1.2rem;
        }

        .user-info {
            font-size: 0.9rem;
        }
    }

    @media (max-width: 576px) {
        .police-main-content {
            padding: 20px 15px;
        }
    }
</style>

<!-- Header -->
<header class="police-header">
    <button class="hamburger" id="sidebarToggle">
        <i class="fas fa-bars"></i>
    </button>
    <span class="badge-icon"></span>
    <h1>Police Portal Pro</h1>
    <div class="user-info">
        Logged in as <strong><?= htmlspecialchars($badge_id) ?></strong>
    </div>
</header>

<!-- Sidebar -->
<nav class="police-sidebar" id="sidebar">
    <div class="sidebar-header">
        <?php if ($is_admin): ?>
            <span class="badge bg-warning text-dark role-badge">
                <i class="fas fa-shield-alt me-2"></i>Administrator
            </span>
        <?php else: ?>
            <span class="badge bg-primary role-badge">
                <i class="fas fa-user-shield me-2"></i>Officer
            </span>
        <?php endif; ?>
    </div>

    <ul class="sidebar-nav">
        <!-- Dashboard Link -->
        <li class="nav-item">
            <a href="/dashboard.php?page=dashboard" class="nav-link <?= ($active_page === 'dashboard' || ($current_page === 'dashboard.php' && empty($page_param))) ? 'active' : '' ?>">
                <i class="fas fa-tachometer-alt"></i>
                Dashboard
            </a>
        </li>

        <?php if ($is_admin): ?>
            <li class="nav-item">
                <a href="/dashboard.php?page=admin_officer_management" class="nav-link <?= $active_page === 'admin_officer_management' ? 'active' : '' ?>">
                    <i class="fas fa-users-cog"></i>
                    Officer Management
                </a>
            </li>
            <li class="nav-item">
                <a href="/dashboard.php?page=admin_permission_matrix" class="nav-link <?= $active_page === 'admin_permission_matrix' ? 'active' : '' ?>">
                    <i class="fas fa-key"></i>
                    Permission Matrix Editor
                </a>
            </li>
            <li class="nav-item">
                <a href="/dashboard.php?page=admin_form_builder" class="nav-link <?= $active_page === 'admin_form_builder' ? 'active' : '' ?>">
                    <i class="fas fa-wpforms"></i>
                    Form Builder
                </a>
            </li>
            <li class="nav-item">
                <a href="/dashboard.php?page=admin_form_submissions" class="nav-link <?= $active_page === 'admin_form_submissions' ? 'active' : '' ?>">
                    <i class="fas fa-inbox"></i>
                    Form Submissions
                </a>
            </li>
            <li class="nav-item">
                <a href="/dashboard.php?page=admin_hierarchy_manager" class="nav-link <?= $active_page === 'admin_hierarchy_manager' ? 'active' : '' ?>">
                    <i class="fas fa-sitemap"></i>
                    Hierarchy Manager
                </a>
            </li>
            <li class="nav-item">
                <a href="/dashboard.php?page=admin_audit_log" class="nav-link <?= $active_page === 'admin_audit_log' ? 'active' : '' ?>">
                    <i class="fas fa-search"></i>
                    Audit Log Explorer
                </a>
            </li>
            <li class="nav-item">
                <a href="/dashboard.php?page=admin_backup_controller" class="nav-link <?= $active_page === 'admin_backup_controller' ? 'active' : '' ?>">
                    <i class="fas fa-database"></i>
                    Backup Controller
                </a>
            </li>
        <?php else: ?>
            <li class="nav-item">
                <a href="/dashboard.php?page=user_reporting_calendar" class="nav-link <?= $active_page === 'user_reporting_calendar' ? 'active' : '' ?>">
                    <i class="fas fa-chart-line"></i>
                    Reporting Calendar
                </a>
            </li>
            <li class="nav-item">
                <a href="/dashboard.php?page=user_chat" class="nav-link <?= $active_page === 'user_chat' ? 'active' : '' ?>">
                    <i class="fas fa-comments"></i>
                    Chat Interface
                </a>
            </li>
            <li class="nav-item">
                <a href="/dashboard.php?page=user_forms" class="nav-link <?= $active_page === 'user_forms' ? 'active' : '' ?>">
                    <i class="fas fa-file-alt"></i>
                    My Forms
                </a>
            </li>
            <li class="nav-item">
                <a href="/dashboard.php?page=user_file_vault" class="nav-link <?= $active_page === 'user_file_vault' ? 'active' : '' ?>">
                    <i class="fas fa-folder-open"></i>
                    File Vault
                </a>
            </li>
            <li class="nav-item">
                <a href="/dashboard.php?page=user_station_map" class="nav-link <?= $active_page === 'user_station_map' ? 'active' : '' ?>">
                    <i class="fas fa-map-marked-alt"></i>
                    Station Map
                </a>
            </li>
        <?php endif; ?>
    </ul>

    <div class="sidebar-footer">
        <form method="POST" action="/logout.php">
            <button type="submit" class="btn btn-danger w-100">
                <i class="fas fa-sign-out-alt me-2"></i>Logout
            </button>
        </form>
    </div>
</nav>

<!-- Navigation JavaScript -->
<script>
    // Sidebar toggle functionality
    document.addEventListener('DOMContentLoaded', function() {
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.querySelector('.police-main-content');

        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', function() {
                if (window.innerWidth <= 768) {
                    sidebar.classList.toggle('show');
                } else {
                    sidebar.classList.toggle('collapsed');
                    if (mainContent) {
                        mainContent.classList.toggle('expanded');
                    }
                }
            });

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(event) {
                if (window.innerWidth <= 768) {
                    if (!sidebar.contains(event.target) && !sidebarToggle.contains(event.target)) {
                        sidebar.classList.remove('show');
                    }
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    sidebar.classList.remove('show');
                    sidebar.classList.remove('collapsed');
                    if (mainContent) {
                        mainContent.classList.remove('expanded');
                    }
                }
            });
        }
    });
</script>
