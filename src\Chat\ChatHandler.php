<?php
// App/Chat/ChatHandler.php
namespace App\Chat;

use Ratchet\MessageComponentInterface;
use Ratchet\ConnectionInterface;
use PDO;

class <PERSON><PERSON><PERSON><PERSON><PERSON> implements MessageComponentInterface {
    protected $clients;
    protected $pdo;
    protected $officerStatus;

    public function __construct(PDO $pdo) {
        $this->clients = new \SplObjectStorage;
        $this->pdo = $pdo;
        $this->officerStatus = [];
    }

    public function onOpen(ConnectionInterface $conn) {
        $this->clients->attach($conn);
        // echo "New connection! ({$conn->resourceId})\n"; // Commented out to reduce log noise
    }

    public function onMessage(ConnectionInterface $from, $msg) {
        $data = json_decode($msg, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("Invalid JSON received from connection {$from->resourceId}: {$msg}");
            $from->send(json_encode(['status' => 'error', 'message' => 'Invalid JSON format.']));
            return;
        }

        // echo "Connection {$from->resourceId} sending message \"" . $msg . "\" to " . count($this->clients) . " other connection(s)\n"; // Commented out

        switch ($data['type']) {
            case 'chat_message':
                $content = $data['content'] ?? null;
                $senderId = $data['officer_id'] ?? null;
                $channelId = $data['channel_id'] ?? null;
                
                // NEW: Extract file-related data from the message
                $filePath = $data['file_path'] ?? null;
                $fileName = $data['file_name'] ?? null;
                $fileSize = $data['file_size'] ?? null;
                $fileType = $data['file_type'] ?? null;

                // A message must have content OR a file, and always a sender and channel
                if ((!$content && !$filePath) || !$senderId || !$channelId) {
                    $from->send(json_encode(['status' => 'error', 'message' => 'Missing text content or file data, sender, or channel.']));
                    return;
                }

                try {
                    $messageId = vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex(random_bytes(16)), 4));

                    // NEW: Update the SQL query to include file fields
                    $stmt = $this->pdo->prepare("INSERT INTO CHAT_MESSAGE (id, sender_id, content, created_at, channel_id, file_path, file_name, file_size, file_type) VALUES (:id, :sender_id, :content, NOW(), :channel_id, :file_path, :file_name, :file_size, :file_type)");
                    $stmt->execute([
                        ':id' => $messageId,
                        ':sender_id' => $senderId,
                        ':content' => $content,
                        ':channel_id' => $channelId,
                        ':file_path' => $filePath,    // NEW: Bind file path
                        ':file_name' => $fileName,    // NEW: Bind file name
                        ':file_size' => $fileSize,    // NEW: Bind file size
                        ':file_type' => $fileType     // NEW: Bind file type
                    ]);

                    // Prepare message data to send to clients
                    // NEW: Include file fields in the broadcasted message
                    $messageData = [
                        'type' => 'chat_message',
                        'id' => $messageId,
                        'officer_id' => $senderId,
                        'content' => $content,
                        'created_at' => date('Y-m-d H:i:s'), // Current time for immediate display
                        'channel_id' => $channelId,
                        'file_path' => $filePath,    // NEW: Include file path
                        'file_name' => $fileName,    // NEW: Include file name
                        'file_size' => $fileSize,    // NEW: Include file size
                        'file_type' => $fileType     // NEW: Include file type
                    ];

                    // Broadcast message to all connected clients
                    foreach ($this->clients as $client) {
                        $client->send(json_encode($messageData));
                    }
                    // echo "Message saved and broadcasted.\n"; // Commented out

                } catch (\PDOException $e) {
                    error_log("Database error saving chat message: " . $e->getMessage());
                    $from->send(json_encode(['status' => 'error', 'message' => 'Failed to save message.']));
                }
                break;

            case 'online_status_update':
                $officerId = $data['officer_id'] ?? null;
                $status = $data['status'] ?? 'offline'; // 'online' or 'offline'

                if ($officerId) {
                    $this->officerStatus[$officerId] = $status;
                    $statusUpdate = ['type' => 'online_status_update', 'officer_id' => $officerId, 'status' => $status];
                    foreach ($this->clients as $client) {
                        $client->send(json_encode($statusUpdate));
                    }
                    // echo "Officer {$officerId} is now {$status}\n"; // Commented out
                }
                break;
            
            case 'user_id_assignment':
                // This type is typically used by the frontend to associate a user ID with a connection.
                // You might want to store this on the ConnectionInterface object for later use.
                // For example: $from->officerId = $data['officer_id'];
                break;

            default:
                // Handle unknown message types
                error_log("Unknown message type received: " . ($data['type'] ?? 'N/A'));
                $from->send(json_encode(['status' => 'error', 'message' => 'Unknown message type.']));
                break;
        }
    }

    public function onClose(ConnectionInterface $conn) {
        $this->clients->detach($conn);
        // You might want to update the officer's status to 'offline' here
        // based on the officer_id stored in onOpen or user_id_assignment.
        // For now, it just detaches the connection.
        // echo "Connection {$conn->resourceId} has disconnected\n"; // Commented out
    }

    public function onError(ConnectionInterface $conn, \Exception $e) {
        // Log actual errors. This message will still appear in your console.
        error_log("WebSocket Error: {$e->getMessage()} on connection {$conn->resourceId}");
        $conn->close();
    }
}