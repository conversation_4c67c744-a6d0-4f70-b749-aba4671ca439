<?php
// Police Portal Pro: Entry Point
// Enforce HTTPS, session security, police branding

declare(strict_types=1);

session_set_cookie_params([
    'lifetime' => 900, // 15 min
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Strict',
]);
session_start();

// if (empty($_SERVER['HTTPS']) || $_SERVER['HTTPS'] === 'off') {
//     header('Location: https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']);
//     exit;
// }

// Branding variables
$policeBlue = '#002366';
$policeGold = '#FFD700';
$policeWhite = '#FFFFFF';

?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Police Portal Pro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@3.3.0/dist/tailwind.min.css" rel="stylesheet">
    <style>
        :root {
            --police-blue: <?= $policeBlue ?>;
            --police-gold: <?= $policeGold ?>;
            --police-white: <?= $policeWhite ?>;
        }
        .header {
            background: linear-gradient(to right, var(--police-blue), #001a4d);
            border-bottom: 3px solid var(--police-gold);
        }
        .badge-icon::before {
            content: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="%23002366" stroke-width="2"><circle cx="12" cy="12" r="10" stroke="%23FFD700" stroke-width="3"/><text x="12" y="16" font-size="12" text-anchor="middle" fill="%23002366">PD</text></svg>');
        }
        body {
            background-color: var(--police-white);
        }
    </style>
</head>
<body>
    <header class="header py-3 d-flex align-items-center">
        <span class="badge-icon me-3"></span>
        <h1 class="mb-0 text-white" style="letter-spacing:2px;">Police Portal Pro</h1>
    </header>
    <main class="container my-5" style="max-width:400px;">
        <div class="card shadow">
            <div class="card-body">
                <h2 class="card-title mb-4" style="color:var(--police-blue);">Login</h2>
                <?php if (!empty($_SESSION['login_error'])): ?>
                    <div class="alert alert-danger" role="alert">
                        <?= htmlspecialchars($_SESSION['login_error']) ?>
                    </div>
                    <?php unset($_SESSION['login_error']); ?>
                <?php endif; ?>
                <form method="POST" action="/login.php" autocomplete="off">
                    <div class="mb-3">
                        <label for="badge_id" class="form-label">Badge ID / Username</label>
                        <input type="text" class="form-control" id="badge_id" name="badge_id" required maxlength="50" placeholder="Enter your badge ID or username">
                        <div class="form-text">Use your badge ID (legacy) or username (new system)</div>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required minlength="6" placeholder="Enter your password">
                        <div class="form-text">Minimum 6 characters (legacy users: 12+ chars with special characters)</div>
                    </div>
                    <button type="submit" class="btn btn-primary w-100" style="background-color:var(--police-blue);border-color:var(--police-gold);">Login</button>
                </form>
            </div>
        </div>
    </main>
</body>
</html>
