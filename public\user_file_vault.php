<?php
/**
 * Police Portal Pro - User File Vault
 *
 * Allows authenticated officers to securely upload, search, and download files.
 * Uses police branding and enforces authentication via _auth_user.php.
 *
 * @package PolicePortalPro
 * <AUTHOR> Name/Team]
 * @version  1.0
 */
declare(strict_types=1);
require_once __DIR__ . '/_auth_user.php'; // Ensures user is authenticated
require_once __DIR__ . '/../config/db.php'; // Database connection for file operations

// Officer badge ID must be set after authentication
if (!isset($_SESSION['officer_badge_id']) || empty($_SESSION['officer_badge_id'])) {
    die('<div class="alert alert-danger">You must be logged in to upload and view files.</div>');
}
$current_officer_badge_id = $_SESSION['officer_badge_id'];

// --- PHP Backend Logic for File Vault ---
$db = $pdo ?? null; // Use $pdo if set by db.php
$messages = [];
$search = [
    'query' => '',
    'officer_badge_id' => '',
    'start_date' => '',
    'end_date' => '',
    'mime_type' => ''
];
$files = [];
$showUploadSuccess = false;
$showUploadError = false;
$suggestedTags = [];
$finalTags = [];
$showSuggestedTags = false;

// --- Handle File Upload ---
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['policeFile'])) {
    $shouldRedirect = false;
    $file = $_FILES['policeFile'];
    $officerId = $current_officer_badge_id;
    
    // Get the original filename directly from the uploaded file array
    $originalFilename = $file['name'];

    $mimeType = $file['type'] ?? 'application/octet-stream';
    $sizeBytes = $file['size'];
    $tags = [];
    if (!empty($_POST['manualTags'])) {
        $tags = array_filter(array_map('trim', explode(',', $_POST['manualTags'])));
    }
    $finalTags = $tags;

    $maxSize = 25 * 1024 * 1024; // 25 MB

    if ($file['error'] !== UPLOAD_ERR_OK) {
        $messages[] = ['type' => 'danger', 'text' => 'File upload error.'];
        $showUploadError = true;
    } elseif ($sizeBytes > $maxSize) {
        $messages[] = ['type' => 'warning', 'text' => 'File size exceeds 25MB. Please select a smaller file.'];
        $showUploadError = true;
    } else {
        // Define the uploads directory
        $uploadsDir = __DIR__ . '/../uploads';
        // Create the directory if it doesn't exist
        if (!is_dir($uploadsDir)) {
            mkdir($uploadsDir, 0755, true);
        }

        // --- THE KEY CHANGE: Generate a truly unique filename ---
        // Combine a unique ID, a hash of the original filename (for added entropy),
        // and a timestamp to ensure extreme uniqueness.
        // Also, append the correct file extension.
        $fileExtension = pathinfo($originalFilename, PATHINFO_EXTENSION);
        $uniqueName = uniqid('file_', true) . '_' . md5($originalFilename . microtime()) . '.' . $fileExtension;
        $filePath = $uploadsDir . '/' . $uniqueName;

        // --- START DEBUGGING LINES ---
        // echo "<h2>DEBUGGING FILE UPLOAD</h2>";
        // echo "<p>Original Filename: <strong>" . htmlspecialchars($originalFilename) . "</strong></p>";
        // echo "<p>Generated Unique Stored Name: <strong>" . htmlspecialchars($uniqueName) . "</strong></p>";
        // echo "<p>Full Path to Save: <strong>" . htmlspecialchars($filePath) . "</strong></p>";
        // echo "<hr>";
        // --- END DEBUGGING LINES ---

        if (move_uploaded_file($file['tmp_name'], $filePath)) {
            // Insert file details into the database
            $stmt = $db->prepare('INSERT INTO police_files (original_filename, stored_filename, mime_type, size_bytes, officer_badge_id, tags, uploaded_at) VALUES (?, ?, ?, ?, ?, ?, NOW())');
            $stmt->execute([
                $originalFilename,
                $uniqueName, // Store the newly generated unique name
                $mimeType,
                $sizeBytes,
                $officerId,
                json_encode($tags)
            ]);
            $messages[] = ['type' => 'success', 'text' => 'File uploaded successfully.'];
            $showUploadSuccess = true;
            $shouldRedirect = true;
        } else {
            $messages[] = ['type' => 'danger', 'text' => 'Failed to save file.'];
            $showUploadError = true;
        }
    }
    // Redirect after upload to clear POST and search filters, preventing re-submission on refresh
    if ($shouldRedirect) {
        header('Location: ' . strtok($_SERVER['REQUEST_URI'], '?'));
        exit;
    }
}

// --- Handle Search (GET) and Fetch Files for View/Search Section ---
$section = $_GET['section'] ?? 'view';
if ($section === 'search') {
    // Only fetch files if a search query parameter is present (i.e., filter applied)
    if (isset($_GET['searchQuery']) || isset($_GET['searchOfficerId']) || isset($_GET['searchStartDate']) || isset($_GET['searchEndDate']) || isset($_GET['searchMimeType'])) {
        $search['query'] = trim($_GET['searchQuery'] ?? '');
        $search['officer_badge_id'] = trim($_GET['searchOfficerId'] ?? '');
        $search['start_date'] = trim($_GET['searchStartDate'] ?? '');
        $search['end_date'] = trim($_GET['searchEndDate'] ?? '');
        $search['mime_type'] = trim($_GET['searchMimeType'] ?? '');

        $sql = 'SELECT * FROM police_files WHERE 1=1';
        $params = [];
        if (!empty($search['query'])) {
            $sql .= ' AND (original_filename LIKE ? OR tags LIKE ?)';
            $params[] = '%' . $search['query'] . '%';
            $params[] = '%' . $search['query'] . '%';
        }
        if (!empty($search['officer_badge_id'])) {
            $sql .= ' AND officer_badge_id = ?';
            $params[] = $search['officer_badge_id'];
        }
        if (!empty($search['start_date'])) {
            $sql .= ' AND uploaded_at >= ?';
            $params[] = $search['start_date'] . ' 00:00:00';
        }
        if (!empty($search['end_date'])) {
            $sql .= ' AND uploaded_at <= ?';
            $params[] = $search['end_date'] . ' 23:59:59';
        }
        if (!empty($search['mime_type'])) {
            $mimeTypes = explode(',', $search['mime_type']);
            if (count($mimeTypes) > 1) {
                $mimeConditions = [];
                foreach ($mimeTypes as $mt) {
                    $mimeConditions[] = 'mime_type LIKE ?';
                    $params[] = '%' . trim($mt) . '%';
                }
                $sql .= ' AND (' . implode(' OR ', $mimeConditions) . ')';
            } else {
                $sql .= ' AND mime_type LIKE ?';
                $params[] = '%' . $search['mime_type'] . '%';
            }
        }
        $sql .= ' ORDER BY uploaded_at DESC';
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $files = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } else {
        // If 'search' section is active but no search parameters are present,
        // don't fetch any files yet. The $files array remains empty.
        $files = [];
    }
} else {
    // In 'view' mode, show ALL files from ALL officers, no filters
    $sql = 'SELECT * FROM police_files ORDER BY uploaded_at DESC';
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $files = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// --- (Optional) Generate Suggested Tags (simple demo: suggest by file name keywords) ---
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Vault - Police Portal Pro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        /* Police Branding */
        :root {
            --police-blue: #002366;
            --police-gold: #FFD700;
            --police-white: #FFFFFF;
        }
        .header-police {
            background: linear-gradient(to right, var(--police-blue), #001a4d);
            border-bottom: 3px solid var(--police-gold);
            color: var(--police-white);
        }
        .btn-police {
            background-color: var(--police-blue);
            color: var(--police-white);
            border: 1px solid var(--police-gold);
            transition: background-color 0.3s ease;
        }
        .btn-police:hover {
            background-color: #001a4d;
            color: var(--police-gold);
        }
        .card-police {
            border: 1px solid rgba(0, 35, 102, 0.1);
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .text-police-gold {
            color: var(--police-gold);
        }
        .bg-police-blue {
            background-color: var(--police-blue);
        }
        .rounded-xl {
            border-radius: 0.75rem;
        }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        .loading-overlay.visible {
            opacity: 1;
            visibility: visible;
        }
        .spinner-border {
            width: 3rem;
            height: 3rem;
        }
    </style>
</head>
<body class="bg-gray-100 font-sans antialiased">
    <div class="header-police p-4 shadow-md flex justify-between items-center">
        <h1 class="text-3xl font-bold">Police Portal Pro - File Vault</h1>
        <a href="/dashboard.php" class="btn btn-police px-4 py-2 rounded-xl text-lg flex items-center space-x-2">
            <i class="fas fa-arrow-left"></i>
            <span>Back to Dashboard</span>
        </a>
    </div>

    <div class="container mx-auto p-6 my-8 bg-white rounded-xl shadow-lg">
    <p class="text-gray-700 mb-6">Manage your files securely. Upload new documents, search for existing ones, and view their details.</p>

    <?php
        $section = $_GET['section'] ?? 'view';
        $validSections = ['upload', 'search', 'view'];
        if (!in_array($section, $validSections)) $section = 'view';
    ?>
    <ul class="nav nav-pills mb-4" style="font-size:1.1rem">
        <li class="nav-item">
            <a class="nav-link<?= $section === 'upload' ? ' active' : '' ?>" href="?section=upload">Upload</a>
        </li>
        <li class="nav-item">
            <a class="nav-link<?= $section === 'search' ? ' active' : '' ?>" href="?section=search">Search</a>
        </li>
        <li class="nav-item">
            <a class="nav-link<?= $section === 'view' ? ' active' : '' ?>" href="?section=view">View All Files</a>
        </li>
    </ul>

    <div class="mt-3">
        <?php foreach ($messages as $msg): ?>
            <div class="alert alert-<?= htmlspecialchars($msg['type']) ?> alert-dismissible fade show" role="alert">
                <?= htmlspecialchars($msg['text']) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endforeach; ?>
    </div>

    <?php if ($section === 'upload'): ?>
    <div class="mb-8 p-6 bg-blue-50 rounded-xl card-police">
        <h2 class="text-2xl font-semibold text-police-blue mb-4 flex items-center space-x-2">
            <i class="fas fa-upload text-police-gold"></i>
            <span>Upload New File</span>
        </h2>
        <form id="uploadForm" method="POST" enctype="multipart/form-data" class="space-y-4">
                <input type="hidden" id="officerBadgeId" value="<?= htmlspecialchars($current_officer_badge_id); ?>">
                <div>
                    <label for="fileInput" class="block text-sm font-medium text-gray-700 mb-1">Select File (Max 25MB)</label>
                    <input type="file" id="fileInput" name="policeFile" class="form-control" accept=".pdf,.doc,.docx,.xlsx,.jpg,.jpeg,.png,.gif,.mp4,.mp3,.zip" required>
                    <p class="text-sm text-gray-500 mt-1">Supported formats: PDF, DOC, DOCX, XLSX, JPG, JPEG, PNG, GIF, MP4, MP3, ZIP</p>
                </div>
                <div id="suggestedTagsDisplay" class="hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Suggested Tags:</label>
                    <div id="tagsList" class="flex flex-wrap gap-2 mb-2"></div>
                    <label for="manualTags" class="block text-sm font-medium text-gray-700 mb-1">Add/Edit Tags (comma-separated):</label>
                    <input type="text" id="manualTags" name="manualTags" class="form-control" placeholder="e.g., incident, report, evidence">
                    <p class="text-sm text-gray-500 mt-1">You can modify or add more tags here.</p>
                </div>
                <button type="submit" class="btn btn-police px-5 py-2 rounded-xl text-lg font-medium">
                    <i class="fas fa-cloud-upload-alt mr-2"></i>Upload File
                </button>
                <div id="uploadMessage" class="mt-3"></div>
            </form>
        </div>
        <?php endif; ?>

        <?php if ($section === 'search'): ?>
        <div class="mb-8 p-6 bg-blue-50 rounded-xl card-police">
            <h2 class="text-2xl font-semibold text-police-blue mb-4 flex items-center space-x-2">
                <i class="fas fa-search text-police-gold"></i>
                <span>Search Files</span>
            </h2>
            <form id="searchForm" method="GET" class="space-y-4">
                <input type="hidden" name="section" value="search">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="searchQuery" class="block text-sm font-medium text-gray-700 mb-1">Keyword Search</label>
                        <input type="text" id="searchQuery" name="searchQuery" class="form-control" placeholder="e.g., report, crime, 2025, witness" value="<?= htmlspecialchars($search['query']) ?>">
                    </div>
                    <div>
                        <label for="searchOfficerId" class="block text-sm font-medium text-gray-700 mb-1">Officer Badge ID</label>
                        <input type="text" id="searchOfficerId" name="searchOfficerId" class="form-control" placeholder="e.g., badge123" value="<?= htmlspecialchars($search['officer_badge_id']) ?>">
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                        <label for="searchStartDate" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                        <input type="date" id="searchStartDate" name="searchStartDate" class="form-control" value="<?= htmlspecialchars($search['start_date']) ?>">
                    </div>
                    <div>
                        <label for="searchEndDate" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                        <input type="date" id="searchEndDate" name="searchEndDate" class="form-control" value="<?= htmlspecialchars($search['end_date']) ?>">
                    </div>
                    <div>
                        <label for="searchMimeType" class="block text-sm font-medium text-gray-700 mb-1">File Type</label>
                        <select id="searchMimeType" name="searchMimeType" class="form-select">
                            <option value=""<?= empty($search['mime_type']) ? ' selected' : '' ?>>Any Type</option>
                            <option value="application/pdf"<?= ($search['mime_type'] ?? '') === 'application/pdf' ? ' selected' : '' ?>>PDF Document</option>
                            <option value="application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"<?= (str_contains($search['mime_type'] ?? '', 'wordprocessingml.document')) ? ' selected' : '' ?>>Word Document</option>
                            <option value="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"<?= (str_contains($search['mime_type'] ?? '', 'spreadsheetml.sheet')) ? ' selected' : '' ?>>Excel Spreadsheet</option>
                            <option value="image/jpeg,image/png,image/gif"<?= (str_contains($search['mime_type'] ?? '', 'image/jpeg')) ? ' selected' : '' ?>>Image</option>
                            <option value="video/mp4"<?= ($search['mime_type'] ?? '') === 'video/mp4' ? ' selected' : '' ?>>MP4 Video</option>
                            <option value="audio/mpeg"<?= ($search['mime_type'] ?? '') === 'audio/mpeg' ? ' selected' : '' ?>>MP3 Audio</option>
                            <option value="application/zip"<?= ($search['mime_type'] ?? '') === 'application/zip' ? ' selected' : '' ?>>ZIP Archive</option>
                        </select>
                    </div>
                </div>
                <button type="submit" class="btn btn-police px-5 py-2 rounded-xl text-lg font-medium">
                    <i class="fas fa-filter mr-2"></i>Apply Filters
                </button>
            </form>
            <div class="p-6 bg-blue-50 rounded-xl card-police mt-4">
                <h2 class="text-2xl font-semibold text-police-blue mb-4 flex items-center space-x-2">
                    <i class="fas fa-folder-open text-police-gold"></i>
                    <span>Search Results</span>
                </h2>
                <div class="overflow-x-auto">
                    <?php if (empty($files)): ?>
                        <p class="text-gray-600" id="noFilesMessage">No files found. Apply filters to see results.</p>
                    <?php else: ?>
                        <table class="table-auto w-full border-collapse border border-gray-300" id="filesTable">
                            <thead class="bg-gray-200">
                                <tr>
                                    <th class="px-4 py-2 text-left text-police-blue">Filename</th>
                                    <th class="px-4 py-2 text-left text-police-blue">Type</th>
                                    <th class="px-4 py-2 text-left text-police-blue">Size</th>
                                    <th class="px-4 py-2 text-left text-police-blue">Uploaded By</th>
                                    <th class="px-4 py-2 text-left text-police-blue">Tags</th>
                                    <th class="px-4 py-2 text-left text-police-blue">Uploaded At</th>
                                    <th class="px-4 py-2 text-left text-police-blue">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="filesTableBody">
                                <?php foreach ($files as $file): ?>
                                    <tr class="hover:bg-gray-50 transition-colors duration-200">
                                        <td class="px-4 py-2 border-b border-gray-200 text-police-blue font-medium">
                                            <?= htmlspecialchars($file['original_filename']) ?>
                                        </td>
                                        <td class="px-4 py-2 border-b border-gray-200 text-sm text-gray-700">
                                            <?= htmlspecialchars($file['mime_type']) ?>
                                        </td>
                                        <td class="px-4 py-2 border-b border-gray-200 text-sm text-gray-700">
                                            <?= number_format($file['size_bytes'] / 1024 / 1024, 2) ?> MB
                                        </td>
                                        <td class="px-4 py-2 border-b border-gray-200 text-sm text-gray-700">
                                            <?= htmlspecialchars($file['officer_badge_id']) ?>
                                        </td>
                                        <td class="px-4 py-2 border-b border-gray-200 text-sm text-gray-700">
                                            <?php
                                                $tags = json_decode($file['tags'], true);
                                                if (is_array($tags) && !empty($tags)) {
                                                    echo htmlspecialchars(implode(', ', $tags));
                                                } else {
                                                    echo 'N/A';
                                                }
                                            ?>
                                        </td>
                                        <td class="px-4 py-2 border-b border-gray-200 text-sm text-gray-700">
                                            <?= htmlspecialchars(date('Y-m-d H:i:s', strtotime($file['uploaded_at']))) ?>
                                        </td>
                                        <td class="px-4 py-2 border-b border-gray-200">
                                            <a href="file_download.php?id=<?= urlencode((string)$file['id']) ?>" target="_blank" class="text-police-blue hover:text-police-gold mr-2" title="Download File">
                                                <i class="fas fa-download"></i> 
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($section === 'view'): ?>
        <div class="p-6 bg-blue-50 rounded-xl card-police">
            <h2 class="text-2xl font-semibold text-police-blue mb-4 flex items-center space-x-2">
                <i class="fas fa-folder-open text-police-gold"></i>
                <span>Your Files</span>
            </h2>
            <div class="overflow-x-auto">
                <?php if (empty($files)): ?>
                    <p class="text-gray-600" id="noFilesMessage">No files found. Try uploading one or adjusting your search.</p>
                <?php else: ?>
                    <table class="table-auto w-full border-collapse border border-gray-300" id="filesTable">
                        <thead class="bg-gray-200">
                            <tr>
                                <th class="px-4 py-2 text-left text-police-blue">Filename</th>
                                <th class="px-4 py-2 text-left text-police-blue">Type</th>
                                <th class="px-4 py-2 text-left text-police-blue">Size</th>
                                <th class="px-4 py-2 text-left text-police-blue">Uploaded By</th>
                                <th class="px-4 py-2 text-left text-police-blue">Tags</th>
                                <th class="px-4 py-2 text-left text-police-blue">Uploaded At</th>
                                <th class="px-4 py-2 text-left text-police-blue">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="filesTableBody">
                            <?php foreach ($files as $file): ?>
    <tr class="hover:bg-gray-50 transition-colors duration-200">
        <td class="px-4 py-2 border-b border-gray-200 text-police-blue font-medium">
            <?= htmlspecialchars($file['original_filename'] ?? '') ?>
        </td>
        <td class="px-4 py-2 border-b border-gray-200 text-sm text-gray-700">
            <?= htmlspecialchars($file['mime_type'] ?? '') ?>
        </td>
        <td class="px-4 py-2 border-b border-gray-200 text-sm text-gray-700">
            <?= isset($file['size_bytes']) ? number_format($file['size_bytes'] / 1024 / 1024, 2) . ' MB' : '' ?>
        </td>
        <td class="px-4 py-2 border-b border-gray-200 text-sm text-gray-700">
            <?= htmlspecialchars($file['officer_badge_id'] ?? '') ?>
        </td>
        <td class="px-4 py-2 border-b border-gray-200 text-sm text-gray-700">
            <?php
                $tags = [];
                if (isset($file['tags'])) {
                    $tags = json_decode($file['tags'], true);
                }
                if (is_array($tags) && !empty($tags)) {
                    echo htmlspecialchars(implode(', ', $tags));
                } else {
                    echo 'N/A';
                }
            ?>
        </td>
        <td class="px-4 py-2 border-b border-gray-200 text-sm text-gray-700">
    <?php
        if (!empty($file['uploaded_at'])) {
            $dt = strtotime($file['uploaded_at']);
            echo $dt ? htmlspecialchars(date('Y-m-d H:i:s', $dt)) : htmlspecialchars($file['uploaded_at']);
        } else {
            echo 'N/A';
        }
    ?>
</td>
<td class="px-4 py-2 border-b border-gray-200">
    <a href="/file_download.php?id=<?= urlencode((string)($file['id'] ?? '')) ?>" target="_blank" class="btn btn-sm btn-police d-inline-flex align-items-center" title="Download File">
        <i class="fas fa-download me-1"></i> Download
    </a>
</td>
    </tr>
<?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>

    <div id="loadingOverlay" class="loading-overlay">
        <div class="spinner-border text-police-blue" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.getElementById('fileInput');
            const suggestedTagsDisplay = document.getElementById('suggestedTagsDisplay');
            const tagsList = document.getElementById('tagsList');
            const manualTagsInput = document.getElementById('manualTags');
            const uploadForm = document.getElementById('uploadForm');
            const loadingOverlay = document.getElementById('loadingOverlay');

            // Function to generate suggested tags
            function generateSuggestedTags(filename) {
                const nameParts = filename
                    .split(/[\s_\-\.]+/); // Split by spaces, underscores, hyphens, dots
                const commonWordsToRemove = new Set(['the', 'a', 'an', 'and', 'of', 'for', 'in', 'on', 'with', 'to', 'from', 'is', 'are', 'was', 'were', 'by', 'as', 'at']);
                
                let uniqueTags = new Set();
                nameParts.forEach(part => {
                    const cleanPart = part.toLowerCase().trim();
                    if (cleanPart.length > 2 && !commonWordsToRemove.has(cleanPart) && isNaN(Number(cleanPart))) {
                        uniqueTags.add(cleanPart);
                    }
                });
                return Array.from(uniqueTags);
            }

            // Event listener for file input change
            fileInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    const filename = this.files[0].name;
                    const suggested = generateSuggestedTags(filename);
                    
                    tagsList.innerHTML = ''; // Clear previous suggestions
                    if (suggested.length > 0) {
                        suggested.forEach(tag => {
                            const badge = document.createElement('span');
                            badge.className = 'badge bg-secondary text-white px-3 py-1 rounded-full text-sm cursor-pointer';
                            badge.textContent = tag;
                            badge.setAttribute('data-tag', tag); // Store tag value
                            badge.addEventListener('click', function() {
                                // Add clicked tag to manualTags input if not already there
                                let currentTags = manualTagsInput.value.split(',').map(t => t.trim()).filter(t => t !== '');
                                if (!currentTags.includes(this.getAttribute('data-tag'))) {
                                    currentTags.push(this.getAttribute('data-tag'));
                                    manualTagsInput.value = currentTags.join(', ');
                                }
                            });
                            tagsList.appendChild(badge);
                        });
                        suggestedTagsDisplay.classList.remove('hidden');
                        // Pre-fill manualTags with suggested tags if it's empty
                        if (manualTagsInput.value === '') {
                             manualTagsInput.value = suggested.join(', ');
                        }
                    } else {
                        suggestedTagsDisplay.classList.add('hidden');
                        tagsList.innerHTML = '';
                        manualTagsInput.value = '';
                    }
                } else {
                    suggestedTagsDisplay.classList.add('hidden');
                    tagsList.innerHTML = '';
                    manualTagsInput.value = '';
                }
            });

            // Show loading overlay on form submission
            uploadForm.addEventListener('submit', function() {
                loadingOverlay.classList.add('visible');
            });
            
            // Handle the case where no files are found after search or initially for search tab
            const filesTableBody = document.getElementById('filesTableBody');
            const filesTable = document.getElementById('filesTable');
            const noFilesMessage = document.getElementById('noFilesMessage');

            // This part is crucial for the "search" section to initially show "No files found."
            // If the section is 'search' and no search parameters are in the URL, hide the table.
            const urlParams = new URLSearchParams(window.location.search);
            const currentSection = urlParams.get('section');
            const hasSearchParams = urlParams.has('searchQuery') || urlParams.has('searchOfficerId') || urlParams.has('searchStartDate') || urlParams.has('searchEndDate') || urlParams.has('searchMimeType');

            if (currentSection === 'search' && !hasSearchParams) {
                if (filesTable) filesTable.classList.add('hidden');
                if (noFilesMessage) noFilesMessage.classList.remove('hidden');
            } else if (filesTableBody.children.length === 0) {
                if (filesTable) filesTable.classList.add('hidden');
                if (noFilesMessage) noFilesMessage.classList.remove('hidden');
            } else {
                if (filesTable) filesTable.classList.remove('hidden');
                if (noFilesMessage) noFilesMessage.classList.add('hidden');
            }
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>