<?php
// chat_server.php

// Suppress PHP Deprecated warnings - THIS MUST BE THE FIRST EXECUTABLE LINE
error_reporting(E_ALL & ~E_DEPRECATED);
ini_set('display_errors', 1); // Keep display errors for actual errors

// Corrected paths for vendor and config - they should be relative to __DIR__
require __DIR__ . '/vendor/autoload.php';   // <-- CORRECTED LINE
require __DIR__ . '/config/config.php';     // <-- CORRECTED LINE

use Ratchet\Server\IoServer;
use Ratchet\Http\HttpServer;
use Ratchet\WebSocket\WsServer;
use App\Chat\ChatHandler;

try {
    $pdo = Config\Database::getConnection();
} catch (\PDOException $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "Starting chat server...\n";
echo "Database connection established.\n";

$server = IoServer::factory(
    new HttpServer(
        new WsServer(
            new ChatHandler($pdo)
        )
    ),
    8080 // Port for the WebSocket server
);

$server->run();