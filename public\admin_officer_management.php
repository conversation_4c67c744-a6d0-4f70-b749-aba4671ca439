<?php
// Admin: User Management System
// Create users and assign them to specific offices
declare(strict_types=1);

require_once __DIR__ . '/_auth_admin.php';
require_once __DIR__ . '/../config/db.php';

// Initialize database tables if they don't exist
function initializeTables(PDO $pdo): void {
    // Create offices table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS offices (
            office_id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            level ENUM('1', '2', '3', '4') NOT NULL,
            location VARCHAR(255),
            permission VARCHAR(50),
            parent_office_id INT,
            FOREIGN KEY (parent_office_id) REFERENCES offices(office_id) ON DELETE RESTRICT,
            INDEX idx_parent_office_id (parent_office_id),
            INDEX idx_level (level)
        ) ENGINE=InnoDB
    ");

    // Create users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            user_id INT PRIMARY KEY AUTO_INCREMENT,
            full_name VARCHAR(100) NOT NULL,
            username VARCHAR(50) NOT NULL UNIQUE,
            password_hash VARCHAR(255) NOT NULL,
            permission VARCHAR(50),
            office_id INT NOT NULL,
            photo VARCHAR(255) DEFAULT NULL,
            FOREIGN KEY (office_id) REFERENCES offices(office_id) ON DELETE RESTRICT,
            INDEX idx_username (username),
            INDEX idx_office_id (office_id)
        ) ENGINE=InnoDB
    ");

    // Add photo column if it doesn't exist (for existing installations)
    try {
        $pdo->exec("ALTER TABLE users ADD COLUMN photo VARCHAR(255) DEFAULT NULL");
    } catch (PDOException $e) {
        // Column already exists, ignore error
    }

    // Insert sample offices if none exist
    $stmt = $pdo->query("SELECT COUNT(*) FROM offices");
    if ($stmt->fetchColumn() == 0) {
        insertSampleOffices($pdo);
    }
}

// Insert sample office data
function insertSampleOffices(PDO $pdo): void {
    $offices = [
        ['Head Office', '4', 'New York HQ', 'admin', null],
        ['Region North', '3', 'Chicago', 'regional', 1],
        ['Region South', '3', 'Atlanta', 'regional', 1],
        ['District A', '2', 'Chicago Downtown', 'district', 2],
        ['District B', '2', 'Chicago Suburbs', 'district', 2],
        ['District C', '2', 'Atlanta Metro', 'district', 3],
        ['Station A1', '1', 'Loop Area', 'local', 4],
        ['Station A2', '1', 'North Side', 'local', 4],
        ['Station B1', '1', 'West Side', 'local', 5],
        ['Station C1', '1', 'Downtown Atlanta', 'local', 6]
    ];

    $stmt = $pdo->prepare("
        INSERT INTO offices (name, level, location, permission, parent_office_id)
        VALUES (?, ?, ?, ?, ?)
    ");

    foreach ($offices as $office) {
        $stmt->execute($office);
    }
}

// Get all offices for dropdown
function getAllOffices(PDO $pdo): array {
    $stmt = $pdo->query("
        SELECT o.office_id, o.name, o.level, o.location, o.permission,
               p.name as parent_name
        FROM offices o
        LEFT JOIN offices p ON o.parent_office_id = p.office_id
        ORDER BY o.level DESC, o.name
    ");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Get office hierarchy for display
function getOfficeHierarchy(PDO $pdo): array {
    $stmt = $pdo->query("
        SELECT office_id, name, level, location, permission, parent_office_id
        FROM offices
        ORDER BY level DESC, name
    ");
    $offices = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return buildOfficeTree($offices);
}

// Build hierarchical tree structure
function buildOfficeTree(array $offices, ?int $parentId = null): array {
    $tree = [];
    foreach ($offices as $office) {
        if ($office['parent_office_id'] == $parentId) {
            $office['children'] = buildOfficeTree($offices, $office['office_id']);
            $tree[] = $office;
        }
    }
    return $tree;
}

// Get all users with their office information
function getAllUsers(PDO $pdo): array {
    $stmt = $pdo->query("
        SELECT u.user_id, u.full_name, u.username, u.permission, u.photo,
               o.name as office_name, o.level as office_level, o.location as office_location
        FROM users u
        JOIN offices o ON u.office_id = o.office_id
        ORDER BY u.full_name
    ");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Create new user
function createUser(PDO $pdo, array $userData): array {
    try {
        // Validate required fields
        $required = ['full_name', 'username', 'password', 'office_id'];
        foreach ($required as $field) {
            if (empty($userData[$field])) {
                throw new Exception("Field '{$field}' is required");
            }
        }

        // Check if username already exists
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
        $stmt->execute([$userData['username']]);
        if ($stmt->fetchColumn() > 0) {
            throw new Exception("Username already exists");
        }

        // Validate office exists
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM offices WHERE office_id = ?");
        $stmt->execute([$userData['office_id']]);
        if ($stmt->fetchColumn() == 0) {
            throw new Exception("Invalid office selected");
        }

        // Hash password
        $passwordHash = password_hash($userData['password'], PASSWORD_DEFAULT);

        // Handle photo upload
        $photoPath = null;
        if (isset($_FILES['photo']) && $_FILES['photo']['error'] === UPLOAD_ERR_OK) {
            $photoPath = handlePhotoUpload($_FILES['photo'], $userData['username']);
            if (!$photoPath) {
                throw new Exception("Failed to upload photo");
            }
        }

        // Insert user
        $stmt = $pdo->prepare("
            INSERT INTO users (full_name, username, password_hash, permission, office_id, photo)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $userData['full_name'],
            $userData['username'],
            $passwordHash,
            $userData['permission'] ?? 'user',
            $userData['office_id'],
            $photoPath
        ]);

        return ['success' => true, 'message' => 'User created successfully'];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Update user
function updateUser(PDO $pdo, int $userId, array $userData): array {
    try {
        // Check if user exists
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE user_id = ?");
        $stmt->execute([$userId]);
        if ($stmt->fetchColumn() == 0) {
            throw new Exception("User not found");
        }

        // Check username uniqueness (excluding current user)
        if (!empty($userData['username'])) {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ? AND user_id != ?");
            $stmt->execute([$userData['username'], $userId]);
            if ($stmt->fetchColumn() > 0) {
                throw new Exception("Username already exists");
            }
        }

        // Build update query dynamically
        $updateFields = [];
        $params = [];

        if (!empty($userData['full_name'])) {
            $updateFields[] = "full_name = ?";
            $params[] = $userData['full_name'];
        }
        if (!empty($userData['username'])) {
            $updateFields[] = "username = ?";
            $params[] = $userData['username'];
        }
        if (!empty($userData['password'])) {
            $updateFields[] = "password_hash = ?";
            $params[] = password_hash($userData['password'], PASSWORD_DEFAULT);
        }
        if (!empty($userData['permission'])) {
            $updateFields[] = "permission = ?";
            $params[] = $userData['permission'];
        }
        if (!empty($userData['office_id'])) {
            // Validate office exists
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM offices WHERE office_id = ?");
            $stmt->execute([$userData['office_id']]);
            if ($stmt->fetchColumn() == 0) {
                throw new Exception("Invalid office selected");
            }
            $updateFields[] = "office_id = ?";
            $params[] = $userData['office_id'];
        }

        // Handle photo upload
        if (isset($_FILES['photo']) && $_FILES['photo']['error'] === UPLOAD_ERR_OK) {
            $photoPath = handlePhotoUpload($_FILES['photo'], $userData['username'] ?? 'user_' . $userId);
            if ($photoPath) {
                $updateFields[] = "photo = ?";
                $params[] = $photoPath;
            }
        }

        if (empty($updateFields)) {
            throw new Exception("No fields to update");
        }

        $params[] = $userId;
        $sql = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE user_id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        return ['success' => true, 'message' => 'User updated successfully'];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Delete user
function deleteUser(PDO $pdo, int $userId): array {
    try {
        $stmt = $pdo->prepare("DELETE FROM users WHERE user_id = ?");
        $stmt->execute([$userId]);

        if ($stmt->rowCount() > 0) {
            return ['success' => true, 'message' => 'User deleted successfully'];
        } else {
            throw new Exception("User not found");
        }
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Handle photo upload
function handlePhotoUpload(array $file, string $username): ?string {
    // Create uploads directory if it doesn't exist
    $uploadDir = __DIR__ . '/uploads/photos/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // Validate file type
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!in_array($file['type'], $allowedTypes)) {
        return null;
    }

    // Validate file size (max 5MB)
    if ($file['size'] > 5 * 1024 * 1024) {
        return null;
    }

    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'user_' . preg_replace('/[^a-zA-Z0-9]/', '_', $username) . '_' . time() . '.' . $extension;
    $filepath = $uploadDir . $filename;

    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return 'uploads/photos/' . $filename;
    }

    return null;
}

// Helper function to get all offices in hierarchy under a specific office
function getOfficeHierarchyIds(PDO $pdo, int $officeId): array {
    $officeIds = [$officeId]; // Include the selected office itself

    // Recursively get all child offices
    $stmt = $pdo->prepare("SELECT office_id FROM offices WHERE parent_office_id = ?");
    $stmt->execute([$officeId]);
    $children = $stmt->fetchAll(PDO::FETCH_COLUMN);

    foreach ($children as $childId) {
        $officeIds = array_merge($officeIds, getOfficeHierarchyIds($pdo, $childId));
    }

    return $officeIds;
}

// Helper function to get users grouped by office for hierarchy
function getUsersByOfficeHierarchy(PDO $pdo, int $officeId): array {
    // Get all office IDs in the hierarchy
    $hierarchyIds = getOfficeHierarchyIds($pdo, $officeId);

    if (empty($hierarchyIds)) {
        return [];
    }

    // Create placeholders for IN clause
    $placeholders = str_repeat('?,', count($hierarchyIds) - 1) . '?';

    // Get all users in the hierarchy grouped by office
    $stmt = $pdo->prepare("
        SELECT u.user_id, u.full_name, u.username, u.permission, u.photo,
               o.office_id, o.name as office_name, o.level as office_level,
               o.location as office_location, o.parent_office_id
        FROM users u
        JOIN offices o ON u.office_id = o.office_id
        WHERE o.office_id IN ($placeholders)
        ORDER BY o.level DESC, o.name, u.full_name
    ");
    $stmt->execute($hierarchyIds);
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Group users by office
    $groupedUsers = [];
    foreach ($users as $user) {
        $officeId = $user['office_id'];
        if (!isset($groupedUsers[$officeId])) {
            $groupedUsers[$officeId] = [
                'office' => [
                    'office_id' => $user['office_id'],
                    'name' => $user['office_name'],
                    'level' => $user['office_level'],
                    'location' => $user['office_location'],
                    'parent_office_id' => $user['parent_office_id']
                ],
                'users' => []
            ];
        }
        $groupedUsers[$officeId]['users'][] = $user;
    }

    return $groupedUsers;
}

// Helper function to get office details with counts
function getOfficeDetailsWithCounts(PDO $pdo, int $officeId): ?array {
    $stmt = $pdo->prepare("
        SELECT office_id, name, level, location, permission, parent_office_id
        FROM offices
        WHERE office_id = ?
    ");
    $stmt->execute([$officeId]);
    $office = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$office) {
        return null;
    }

    // Get direct user count
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE office_id = ?");
    $stmt->execute([$officeId]);
    $directUserCount = $stmt->fetchColumn();

    // Get total user count in hierarchy
    $hierarchyIds = getOfficeHierarchyIds($pdo, $officeId);
    $placeholders = str_repeat('?,', count($hierarchyIds) - 1) . '?';
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE office_id IN ($placeholders)");
    $stmt->execute($hierarchyIds);
    $totalUserCount = $stmt->fetchColumn();

    // Get child office count
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM offices WHERE parent_office_id = ?");
    $stmt->execute([$officeId]);
    $childOfficeCount = $stmt->fetchColumn();

    $office['direct_user_count'] = $directUserCount;
    $office['total_user_count'] = $totalUserCount;
    $office['child_office_count'] = $childOfficeCount;

    return $office;
}

// Helper function to get offices in hierarchy for dropdown
function getOfficesInHierarchy(PDO $pdo, int $officeId): array {
    $hierarchyIds = getOfficeHierarchyIds($pdo, $officeId);

    if (empty($hierarchyIds)) {
        return [];
    }

    $placeholders = str_repeat('?,', count($hierarchyIds) - 1) . '?';
    $stmt = $pdo->prepare("
        SELECT office_id, name, level, location
        FROM offices
        WHERE office_id IN ($placeholders)
        ORDER BY level DESC, name
    ");
    $stmt->execute($hierarchyIds);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Initialize tables
initializeTables($pdo);

// Handle AJAX requests for office details FIRST (before any HTML output)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ajax_action'])) {
    // Prevent any output before JSON
    ob_clean();
    header('Content-Type: application/json');

    try {
        switch ($_POST['ajax_action']) {
            case 'get_office_users':
                $officeId = (int)($_POST['office_id'] ?? 0);
                if ($officeId > 0) {
                    $groupedUsers = getUsersByOfficeHierarchy($pdo, $officeId);
                    $office = getOfficeDetailsWithCounts($pdo, $officeId);
                    $hierarchyOffices = getOfficesInHierarchy($pdo, $officeId);

                    if ($office) {
                        echo json_encode([
                            'success' => true,
                            'grouped_users' => $groupedUsers,
                            'office' => $office,
                            'hierarchy_offices' => $hierarchyOffices
                        ]);
                    } else {
                        echo json_encode(['success' => false, 'error' => 'Office not found']);
                    }
                } else {
                    echo json_encode(['success' => false, 'error' => 'Invalid office ID']);
                }
                exit;

            case 'create_user_for_office':
                $officeId = (int)($_POST['office_id'] ?? 0);
                if ($officeId > 0) {
                    $_POST['office_id'] = $officeId; // Ensure office_id is set
                    $result = createUser($pdo, $_POST);
                    echo json_encode($result);
                } else {
                    echo json_encode(['success' => false, 'error' => 'Invalid office ID']);
                }
                exit;

            default:
                echo json_encode(['success' => false, 'error' => 'Unknown action']);
                exit;
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'Server error: ' . $e->getMessage()]);
        exit;
    }
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'create_user':
            $result = createUser($pdo, $_POST);
            $message = $result['success'] ? $result['message'] : $result['error'];
            $messageType = $result['success'] ? 'success' : 'danger';
            break;

        case 'update_user':
            $userId = (int)($_POST['user_id'] ?? 0);
            $result = updateUser($pdo, $userId, $_POST);
            $message = $result['success'] ? $result['message'] : $result['error'];
            $messageType = $result['success'] ? 'success' : 'danger';
            break;

        case 'delete_user':
            $userId = (int)($_POST['user_id'] ?? 0);
            $result = deleteUser($pdo, $userId);
            $message = $result['success'] ? $result['message'] : $result['error'];
            $messageType = $result['success'] ? 'success' : 'danger';
            break;
    }
}

// Get data for display
$offices = getAllOffices($pdo);
$users = getAllUsers($pdo);
$officeHierarchy = getOfficeHierarchy($pdo);

// Get user for editing if requested
$editUser = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $stmt = $pdo->prepare("
        SELECT u.*, o.name as office_name
        FROM users u
        JOIN offices o ON u.office_id = o.office_id
        WHERE u.user_id = ?
    ");
    $stmt->execute([(int)$_GET['edit']]);
    $editUser = $stmt->fetch(PDO::FETCH_ASSOC);
}
?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - Police Portal Pro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --police-blue: #1e3a8a;
            --police-dark-blue: #1e40af;
            --police-gold: #fbbf24;
            --police-light-blue: #3b82f6;
        }

        body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            min-height: 100vh;
        }

        .police-header {
            background: linear-gradient(135deg, var(--police-blue) 0%, var(--police-dark-blue) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .police-badge {
            background: rgba(255, 255, 255, 0.1);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            font-weight: 600;
        }

        .card {
            border: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .btn-police {
            background: var(--police-blue);
            border-color: var(--police-blue);
            color: white;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-police:hover {
            background: var(--police-dark-blue);
            border-color: var(--police-dark-blue);
            color: white;
            transform: translateY(-1px);
        }

        .office-tree {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
        }

        .office-node {
            cursor: pointer;
            padding: 8px 12px;
            margin: 2px 0;
            border-radius: 5px;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .office-node:hover {
            background: var(--police-gold);
            color: var(--police-blue);
            border-left-color: var(--police-blue);
        }

        .office-node.active {
            background: var(--police-blue);
            color: white;
            border-left-color: var(--police-gold);
        }

        .office-level-1 { margin-left: 0px; }
        .office-level-2 { margin-left: 20px; }
        .office-level-3 { margin-left: 40px; }
        .office-level-4 { margin-left: 60px; }

        .user-card {
            transition: all 0.3s ease;
            border-left: 4px solid var(--police-blue);
            max-height: 280px;
        }

        .user-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(30, 58, 138, 0.15);
        }

        .user-photo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid var(--police-blue);
        }

        .user-photo-placeholder {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--police-blue), var(--police-light-blue));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2em;
            border: 3px solid var(--police-blue);
        }

        .permission-badge {
            font-size: 0.75em;
            font-weight: 600;
        }

        .nav-tabs .nav-link {
            color: var(--police-blue);
            border: none;
            border-radius: 8px 8px 0 0;
            font-weight: 500;
            margin-right: 0.5rem;
        }

        .nav-tabs .nav-link.active {
            background: var(--police-blue);
            color: white;
            border-color: var(--police-blue);
        }

        .nav-tabs .nav-link:hover {
            border-color: transparent;
            color: var(--police-dark-blue);
        }

        .modal-header {
            background: var(--police-blue);
            color: white;
            border-radius: 12px 12px 0 0;
        }

        .modal-content {
            border-radius: 12px;
            border: none;
        }

        .form-control:focus {
            border-color: var(--police-blue);
            box-shadow: 0 0 0 0.2rem rgba(30, 58, 138, 0.25);
        }

        .form-select:focus {
            border-color: var(--police-blue);
            box-shadow: 0 0 0 0.2rem rgba(30, 58, 138, 0.25);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="police-header">
                <div class="container">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="mb-0">
                                <i class="fas fa-users-cog me-3"></i>
                                User Management System
                            </h1>
                            <p class="mb-0 mt-2 opacity-75">
                                Create users and assign them to specific offices in the hierarchy
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="police-badge">
                                <i class="fas fa-shield-alt me-2"></i>
                                Admin Portal
                            </div>
                            <div class="text-white-50 mt-2">
                                <small>
                                    <i class="fas fa-building me-1"></i>
                                    System Administration
                                </small>
                            </div>
                            <div class="text-white-50">
                                <small>
                                    <i class="fas fa-users me-1"></i>
                                    Total Users: <?= count($users) ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="container">
                <!-- Navigation Tabs -->
                <ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="users-tab" data-bs-toggle="tab" data-bs-target="#users-panel" type="button" role="tab">
                            <i class="fas fa-users me-2"></i>Manage Users
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="hierarchy-tab" data-bs-toggle="tab" data-bs-target="#hierarchy-panel" type="button" role="tab">
                            <i class="fas fa-sitemap me-2"></i>Office Hierarchy
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="create-tab" data-bs-toggle="tab" data-bs-target="#create-panel" type="button" role="tab">
                            <i class="fas fa-user-plus me-2"></i>Create User
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="mainTabContent">

                    <?php if ($message): ?>
                        <div class="alert alert-<?= $messageType ?> alert-dismissible fade show" role="alert">
                            <?= htmlspecialchars($message) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Users Management Tab -->
                    <div class="tab-pane fade show active" id="users-panel" role="tabpanel">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-users me-2"></i>All Users (<?= count($users) ?>)
                                </h5>
                                <button class="btn btn-police btn-sm" data-bs-toggle="modal" data-bs-target="#createUserModal">
                                    <i class="fas fa-plus me-1"></i>Create New User
                                </button>
                            </div>
                            <div class="card-body">
                                <?php if (empty($users)): ?>
                                    <div class="text-center py-5">
                                        <i class="fas fa-users fa-4x text-muted mb-3"></i>
                                        <h5 class="text-muted">No users found</h5>
                                        <p class="text-muted">Create your first user to get started!</p>
                                        <button class="btn btn-police" data-bs-toggle="modal" data-bs-target="#createUserModal">
                                            <i class="fas fa-plus me-1"></i>Create First User
                                        </button>
                                    </div>
                                <?php else: ?>
                                    <div class="row">
                                        <?php foreach ($users as $user): ?>
                                            <div class="col-md-6 col-lg-4 col-xl-3 mb-3">
                                                <div class="card user-card h-100">
                                                    <div class="card-body p-3">
                                                        <div class="d-flex align-items-center mb-2">
                                                            <?php if ($user['photo']): ?>
                                                                <img src="<?= htmlspecialchars($user['photo']) ?>" alt="<?= htmlspecialchars($user['full_name']) ?>" class="user-photo">
                                                            <?php else: ?>
                                                                <div class="user-photo-placeholder">
                                                                    <?= strtoupper(substr($user['full_name'], 0, 1)) ?>
                                                                </div>
                                                            <?php endif; ?>
                                                            <div class="ms-3 flex-grow-1">
                                                                <h6 class="mb-0 fw-bold" style="font-size: 0.9em;">
                                                                    <?= htmlspecialchars($user['full_name']) ?>
                                                                </h6>
                                                                <small class="text-muted"><?= htmlspecialchars($user['username']) ?></small>
                                                            </div>
                                                            <span class="badge bg-<?= getPermissionColor($user['permission']) ?> permission-badge">
                                                                <?= htmlspecialchars($user['permission']) ?>
                                                            </span>
                                                        </div>
                                                        <div class="mb-2">
                                                            <small class="text-muted">
                                                                <i class="fas fa-building me-1"></i>
                                                                <?= htmlspecialchars($user['office_name']) ?>
                                                                <span class="badge bg-secondary ms-1" style="font-size: 0.7em;">Level <?= (int)$user['office_level'] ?></span>
                                                            </small>
                                                        </div>
                                                        <div class="d-flex gap-1">
                                                            <a href="?edit=<?= $user['user_id'] ?>"
                                                               class="btn btn-sm btn-outline-primary flex-fill" style="font-size: 0.75em;">
                                                                <i class="fas fa-edit"></i> Edit
                                                            </a>
                                                            <button class="btn btn-sm btn-outline-danger"
                                                                    onclick="confirmDelete(<?= $user['user_id'] ?>, '<?= htmlspecialchars($user['full_name'], ENT_QUOTES) ?>')" style="font-size: 0.75em;">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Office Hierarchy Tab -->
                    <div class="tab-pane fade" id="hierarchy-panel" role="tabpanel">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-sitemap me-2"></i>Office Hierarchy
                                        </h6>
                                    </div>
                                    <div class="card-body p-0">
                                        <div class="office-tree" id="officeTree">
                                            <?php renderOfficeTreeClickable($officeHierarchy, 1); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div id="hierarchyDetails">
                                    <div class="alert alert-info">
                                        <i class="fas fa-hand-pointer me-2"></i>
                                        Select an office from the hierarchy to view its users and manage them.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Create User Tab -->
                    <div class="tab-pane fade" id="create-panel" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-user-plus me-2"></i>Create New User
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" enctype="multipart/form-data">
                                    <input type="hidden" name="action" value="create_user">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="full_name_tab" class="form-label">Full Name *</label>
                                                <input type="text" class="form-control" id="full_name_tab" name="full_name" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="username_tab" class="form-label">Username *</label>
                                                <input type="text" class="form-control" id="username_tab" name="username" required>
                                                <div class="form-text">Must be unique across the system</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label for="photo_tab" class="form-label">Profile Photo</label>
                                                <input type="file" class="form-control" id="photo_tab" name="photo" accept="image/*">
                                                <div class="form-text">Upload a profile photo (JPG, PNG, GIF, WebP - Max 5MB)</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="password_tab" class="form-label">Password *</label>
                                                <input type="password" class="form-control" id="password_tab" name="password" required>
                                                <div class="form-text">Minimum 6 characters recommended</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="permission_tab" class="form-label">Permission Level</label>
                                                <select class="form-select" id="permission_tab" name="permission">
                                                    <option value="user">User</option>
                                                    <option value="supervisor">Supervisor</option>
                                                    <option value="admin">Admin</option>
                                                    <option value="super_admin">Super Admin</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-4">
                                        <label for="office_id_tab" class="form-label">Assign to Office *</label>
                                        <select class="form-select" id="office_id_tab" name="office_id" required>
                                            <option value="">Select an office...</option>
                                            <?php foreach ($offices as $office): ?>
                                                <option value="<?= $office['office_id'] ?>">
                                                    Level <?= $office['level'] ?> - <?= htmlspecialchars($office['name']) ?>
                                                    <?php if ($office['location']): ?>
                                                        (<?= htmlspecialchars($office['location']) ?>)
                                                    <?php endif; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-police">
                                            <i class="fas fa-save me-1"></i>Create User
                                        </button>
                                        <button type="reset" class="btn btn-outline-secondary">
                                            <i class="fas fa-undo me-1"></i>Reset Form
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="/dashboard.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Create User Modal -->
    <div class="modal fade" id="createUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="create_user">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-user-plus me-2"></i>Create New User
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="full_name" class="form-label">Full Name *</label>
                            <input type="text" class="form-control" id="full_name" name="full_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="username" class="form-label">Username *</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                            <div class="form-text">Must be unique across the system</div>
                        </div>
                        <div class="mb-3">
                            <label for="photo" class="form-label">Profile Photo</label>
                            <input type="file" class="form-control" id="photo" name="photo" accept="image/*">
                            <div class="form-text">Upload a profile photo (JPG, PNG, GIF, WebP - Max 5MB)</div>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password *</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <div class="form-text">Minimum 6 characters recommended</div>
                        </div>
                        <div class="mb-3">
                            <label for="permission" class="form-label">Permission Level</label>
                            <select class="form-select" id="permission" name="permission">
                                <option value="user">User</option>
                                <option value="supervisor">Supervisor</option>
                                <option value="admin">Admin</option>
                                <option value="super_admin">Super Admin</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="office_id" class="form-label">Assign to Office *</label>
                            <select class="form-select" id="office_id" name="office_id" required>
                                <option value="">Select an office...</option>
                                <?php foreach ($offices as $office): ?>
                                    <option value="<?= $office['office_id'] ?>">
                                        Level <?= $office['level'] ?> - <?= htmlspecialchars($office['name']) ?>
                                        <?php if ($office['location']): ?>
                                            (<?= htmlspecialchars($office['location']) ?>)
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-police">
                            <i class="fas fa-save me-1"></i>Create User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <?php if ($editUser): ?>
    <div class="modal fade show" id="editUserModal" tabindex="-1" style="display: block;">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="update_user">
                    <input type="hidden" name="user_id" value="<?= $editUser['user_id'] ?>">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-user-edit me-2"></i>Edit User: <?= htmlspecialchars($editUser['full_name']) ?>
                        </h5>
                        <a href="?" class="btn-close"></a>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="edit_full_name" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="edit_full_name" name="full_name"
                                   value="<?= htmlspecialchars($editUser['full_name']) ?>">
                        </div>
                        <div class="mb-3">
                            <label for="edit_username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="edit_username" name="username"
                                   value="<?= htmlspecialchars($editUser['username']) ?>">
                        </div>
                        <?php if ($editUser['photo']): ?>
                            <div class="mb-3">
                                <label class="form-label">Current Photo</label>
                                <div class="d-flex align-items-center">
                                    <img src="<?= htmlspecialchars($editUser['photo']) ?>" alt="Current photo" class="user-photo me-3">
                                    <small class="text-muted">Upload a new photo to replace this one</small>
                                </div>
                            </div>
                        <?php endif; ?>
                        <div class="mb-3">
                            <label for="edit_photo" class="form-label">Profile Photo</label>
                            <input type="file" class="form-control" id="edit_photo" name="photo" accept="image/*">
                            <div class="form-text">Upload a new profile photo (JPG, PNG, GIF, WebP - Max 5MB)</div>
                        </div>
                        <div class="mb-3">
                            <label for="edit_password" class="form-label">New Password</label>
                            <input type="password" class="form-control" id="edit_password" name="password">
                            <div class="form-text">Leave blank to keep current password</div>
                        </div>
                        <div class="mb-3">
                            <label for="edit_permission" class="form-label">Permission Level</label>
                            <select class="form-select" id="edit_permission" name="permission">
                                <option value="user" <?= $editUser['permission'] === 'user' ? 'selected' : '' ?>>User</option>
                                <option value="supervisor" <?= $editUser['permission'] === 'supervisor' ? 'selected' : '' ?>>Supervisor</option>
                                <option value="admin" <?= $editUser['permission'] === 'admin' ? 'selected' : '' ?>>Admin</option>
                                <option value="super_admin" <?= $editUser['permission'] === 'super_admin' ? 'selected' : '' ?>>Super Admin</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="edit_office_id" class="form-label">Assign to Office</label>
                            <select class="form-select" id="edit_office_id" name="office_id">
                                <?php foreach ($offices as $office): ?>
                                    <option value="<?= $office['office_id'] ?>"
                                            <?= $office['office_id'] == $editUser['office_id'] ? 'selected' : '' ?>>
                                        Level <?= $office['level'] ?> - <?= htmlspecialchars($office['name']) ?>
                                        <?php if ($office['location']): ?>
                                            (<?= htmlspecialchars($office['location']) ?>)
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="alert alert-info">
                            <small>
                                <i class="fas fa-info-circle me-1"></i>
                                Currently assigned to: <strong><?= htmlspecialchars($editUser['office_name']) ?></strong>
                            </small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <a href="?" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-police">
                            <i class="fas fa-save me-1"></i>Update User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
    <?php endif; ?>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" id="deleteForm">
                    <input type="hidden" name="action" value="delete_user">
                    <input type="hidden" name="user_id" id="deleteUserId">
                    <div class="modal-header">
                        <h5 class="modal-title text-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to delete user <strong id="deleteUserName"></strong>?</p>
                        <div class="alert alert-warning">
                            <i class="fas fa-warning me-1"></i>
                            This action cannot be undone!
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>Delete User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(userId, userName) {
            document.getElementById('deleteUserId').value = userId;
            document.getElementById('deleteUserName').textContent = userName;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        // Office tree clicks
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-hide alerts after 5 seconds
            const alerts = document.querySelectorAll('.alert-dismissible');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });

            // Office node click handlers
            document.querySelectorAll('.office-node').forEach(node => {
                node.addEventListener('click', function() {
                    selectOffice(this);
                });
            });
        });

        // Office selection
        function selectOffice(element) {
            // Remove active class from all offices
            document.querySelectorAll('.office-node').forEach(node => {
                node.classList.remove('active');
            });

            // Add active class to selected office
            element.classList.add('active');

            const officeId = element.getAttribute('data-office-id');
            const officeLevel = element.getAttribute('data-office-level');
            const officeName = element.getAttribute('data-office-name');
            const officeLocation = element.getAttribute('data-office-location');

            // Load office users
            loadOfficeUsers(officeId, officeLevel, officeName, officeLocation);
        }

        // Load office users
        async function loadOfficeUsers(officeId, officeLevel, officeName, officeLocation) {
            const container = document.getElementById('hierarchyDetails');

            try {
                // Show loading
                container.innerHTML = `
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading office details...</p>
                    </div>
                `;

                const formData = new FormData();
                formData.append('ajax_action', 'get_office_users');
                formData.append('office_id', officeId);

                const response = await fetch(window.location.href, {
                    method: 'POST',
                    body: formData
                });

                // Check if response is ok
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Get response text first to debug
                const responseText = await response.text();
                console.log('Response text:', responseText);

                // Try to parse as JSON
                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('JSON parse error:', parseError);
                    console.error('Response was:', responseText);
                    throw new Error('Invalid JSON response from server');
                }

                if (result.success) {
                    displayOfficeHierarchy(result.office, result.grouped_users, result.hierarchy_offices);
                } else {
                    container.innerHTML = '<div class="alert alert-warning">Error loading office details: ' + (result.error || 'Unknown error') + '</div>';
                }
            } catch (error) {
                console.error('Full error:', error);
                container.innerHTML = '<div class="alert alert-danger">Network error: ' + error.message + '</div>';
            }
        }

        // Display office hierarchy with grouped users
        function displayOfficeHierarchy(office, groupedUsers, hierarchyOffices) {
            const container = document.getElementById('hierarchyDetails');

            const levelName = getLevelName(office.level);
            const totalOffices = Object.keys(groupedUsers).length;

            let html = `
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-building me-2"></i>${office.name}
                            <span class="badge bg-secondary ms-2">${levelName}</span>
                        </h6>
                        <button class="btn btn-police btn-sm" onclick="showCreateUserModalWithHierarchy('${office.office_id}', '${office.name}', ${JSON.stringify(hierarchyOffices).replace(/"/g, '&quot;')})">
                            <i class="fas fa-user-plus me-1"></i>Add User
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h5 class="text-primary mb-0">${office.direct_user_count}</h5>
                                    <small class="text-muted">Direct Users</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h5 class="text-success mb-0">${office.total_user_count}</h5>
                                    <small class="text-muted">Total Users</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h5 class="text-info mb-0">${totalOffices}</h5>
                                    <small class="text-muted">Offices</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h5 class="text-warning mb-0">${office.child_office_count}</h5>
                                    <small class="text-muted">Sub-Offices</small>
                                </div>
                            </div>
                        </div>
            `;

            if (Object.keys(groupedUsers).length === 0) {
                html += `
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No users found in this hierarchy</h6>
                        <p class="text-muted">Create the first user for this office!</p>
                        <button class="btn btn-police" onclick="showCreateUserModalWithHierarchy('${office.office_id}', '${office.name}', ${JSON.stringify(hierarchyOffices).replace(/"/g, '&quot;')})">
                            <i class="fas fa-user-plus me-1"></i>Create First User
                        </button>
                    </div>
                `;
            } else {
                // Display offices with their users
                html += '<div class="accordion" id="officeAccordion">';

                let accordionIndex = 0;
                for (const [officeId, officeData] of Object.entries(groupedUsers)) {
                    const officeInfo = officeData.office;
                    const users = officeData.users;
                    const officeLevelName = getLevelName(officeInfo.level);

                    html += `
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading${accordionIndex}">
                                <button class="accordion-button ${accordionIndex === 0 ? '' : 'collapsed'}" type="button"
                                        data-bs-toggle="collapse" data-bs-target="#collapse${accordionIndex}"
                                        aria-expanded="${accordionIndex === 0 ? 'true' : 'false'}" aria-controls="collapse${accordionIndex}">
                                    <div class="d-flex justify-content-between align-items-center w-100 me-3">
                                        <div>
                                            <i class="fas fa-building me-2"></i>
                                            <strong>${officeInfo.name}</strong>
                                            <span class="badge bg-secondary ms-2">${officeLevelName}</span>
                                            ${officeInfo.location ? `<small class="text-muted ms-2">(${officeInfo.location})</small>` : ''}
                                        </div>
                                        <div>
                                            <span class="badge bg-primary">${users.length} users</span>
                                        </div>
                                    </div>
                                </button>
                            </h2>
                            <div id="collapse${accordionIndex}" class="accordion-collapse collapse ${accordionIndex === 0 ? 'show' : ''}"
                                 aria-labelledby="heading${accordionIndex}" data-bs-parent="#officeAccordion">
                                <div class="accordion-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <small class="text-muted">Users in ${officeInfo.name}</small>
                                        <button class="btn btn-outline-primary btn-sm" onclick="showCreateUserModalWithHierarchy('${officeInfo.office_id}', '${officeInfo.name}', ${JSON.stringify(hierarchyOffices).replace(/"/g, '&quot;')})">
                                            <i class="fas fa-user-plus me-1"></i>Add User Here
                                        </button>
                                    </div>
                                    <div class="row">
                    `;

                    users.forEach(user => {
                        const permissionColor = getPermissionColorJS(user.permission);
                        const photoHtml = user.photo
                            ? `<img src="${user.photo}" alt="${user.full_name}" class="user-photo">`
                            : `<div class="user-photo-placeholder">${user.full_name.charAt(0).toUpperCase()}</div>`;

                        html += `
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card user-card h-100">
                                    <div class="card-body p-3">
                                        <div class="d-flex align-items-center mb-2">
                                            ${photoHtml}
                                            <div class="ms-3 flex-grow-1">
                                                <h6 class="mb-0 fw-bold" style="font-size: 0.9em;">${user.full_name}</h6>
                                                <small class="text-muted">${user.username}</small>
                                            </div>
                                            <span class="badge bg-${permissionColor} permission-badge">${user.permission}</span>
                                        </div>
                                        <div class="d-flex gap-1">
                                            <a href="?edit=${user.user_id}" class="btn btn-sm btn-outline-primary flex-fill" style="font-size: 0.75em;">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <button class="btn btn-sm btn-outline-danger" onclick="confirmDelete(${user.user_id}, '${user.full_name}')" style="font-size: 0.75em;">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });

                    html += `
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    accordionIndex++;
                }

                html += '</div>'; // Close accordion
            }

            html += `
                    </div>
                </div>
            `;

            container.innerHTML = html;
        }

        // Get level name
        function getLevelName(level) {
            switch(level) {
                case '4': return 'Head Office';
                case '3': return 'Region';
                case '2': return 'Branch';
                case '1': return 'Office';
                default: return 'Level ' + level;
            }
        }

        // Get permission color for JavaScript
        function getPermissionColorJS(permission) {
            switch(permission) {
                case 'super_admin': return 'danger';
                case 'admin': return 'warning';
                case 'supervisor': return 'info';
                case 'user': return 'secondary';
                default: return 'secondary';
            }
        }

        // Show create user modal for specific office
        function showCreateUserModal(officeId, officeName) {
            // Set the office in the modal
            const modal = document.getElementById('createUserModal');
            const officeSelect = modal.querySelector('#office_id');

            // Set the selected office
            officeSelect.value = officeId;

            // Update modal title
            const modalTitle = modal.querySelector('.modal-title');
            modalTitle.innerHTML = `<i class="fas fa-user-plus me-2"></i>Create New User for ${officeName}`;

            // Show the modal
            new bootstrap.Modal(modal).show();
        }

        // Show create user modal with hierarchy office options
        function showCreateUserModalWithHierarchy(defaultOfficeId, defaultOfficeName, hierarchyOffices) {
            const modal = document.getElementById('createUserModal');
            const officeSelect = modal.querySelector('#office_id');
            const modalTitle = modal.querySelector('.modal-title');

            // Clear existing options
            officeSelect.innerHTML = '<option value="">Select an office...</option>';

            // Add hierarchy offices to dropdown
            hierarchyOffices.forEach(office => {
                const option = document.createElement('option');
                option.value = office.office_id;
                option.textContent = `Level ${office.level} - ${office.name}${office.location ? ` (${office.location})` : ''}`;
                if (office.office_id == defaultOfficeId) {
                    option.selected = true;
                }
                officeSelect.appendChild(option);
            });

            // Update modal title
            modalTitle.innerHTML = `<i class="fas fa-user-plus me-2"></i>Create New User (Default: ${defaultOfficeName})`;

            // Show the modal
            new bootstrap.Modal(modal).show();
        }
    </script>
</body>
</html>

<?php
// Helper function to render clickable office tree with user counts
function renderOfficeTreeClickable(array $offices, int $level = 1): void {
    global $pdo; // Access PDO for user counts

    foreach ($offices as $office) {
        $levelClass = "office-level-{$level}";
        $typeIcon = match($office['level']) {
            '4' => 'fas fa-building',      // Head Office
            '3' => 'fas fa-home',          // Region
            '2' => 'fas fa-map',           // Branch
            '1' => 'fas fa-map-marker-alt', // Office
            default => 'fas fa-circle'
        };

        $levelName = match($office['level']) {
            '4' => 'Head Office',
            '3' => 'Region',
            '2' => 'Branch',
            '1' => 'Office',
            default => 'Level ' . $office['level']
        };

        // Get user counts for this office
        $directCount = 0;
        $totalCount = 0;

        try {
            // Direct users
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE office_id = ?");
            $stmt->execute([$office['office_id']]);
            $directCount = $stmt->fetchColumn();

            // Total users in hierarchy
            $hierarchyIds = getOfficeHierarchyIds($pdo, $office['office_id']);
            if (!empty($hierarchyIds)) {
                $placeholders = str_repeat('?,', count($hierarchyIds) - 1) . '?';
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE office_id IN ($placeholders)");
                $stmt->execute($hierarchyIds);
                $totalCount = $stmt->fetchColumn();
            }
        } catch (Exception $e) {
            // If error, just show 0
        }

        echo "<div class='office-node {$levelClass}' data-office-id='{$office['office_id']}' data-office-level='{$office['level']}' data-office-name='" . htmlspecialchars($office['name']) . "' data-office-location='" . htmlspecialchars($office['location'] ?? '') . "'>";
        echo "<i class='{$typeIcon} me-2'></i>";
        echo "<strong>" . htmlspecialchars($office['name']) . "</strong>";
        echo "<span class='badge bg-secondary ms-2'>" . htmlspecialchars($levelName) . "</span>";

        // Show user counts
        if ($totalCount > 0) {
            if ($directCount > 0 && $totalCount > $directCount) {
                echo "<br><small class='text-primary ms-4'>{$directCount} direct, {$totalCount} total users</small>";
            } else {
                echo "<br><small class='text-primary ms-4'>{$totalCount} user" . ($totalCount !== 1 ? 's' : '') . "</small>";
            }
        }

        if ($office['location']) {
            echo "<br><small class='text-muted ms-4'>" . htmlspecialchars($office['location']) . "</small>";
        }
        echo "</div>";

        if (!empty($office['children'])) {
            renderOfficeTreeClickable($office['children'], $level + 1);
        }
    }
}

// Helper function to get permission badge color
function getPermissionColor(string $permission): string {
    return match($permission) {
        'super_admin' => 'danger',
        'admin' => 'warning',
        'supervisor' => 'info',
        'user' => 'secondary',
        default => 'secondary'
    };
}
?>
