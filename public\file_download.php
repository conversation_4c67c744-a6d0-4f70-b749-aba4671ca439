<?php
// file_download.php - Direct download handler for police_files table
// This is a fallback for legacy links; /api/file_download.php is the main handler.

require_once __DIR__ . '/../config/db.php';

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    echo 'Invalid file ID.';
    exit;
}

$file_id = (int)$_GET['id'];

try {
    $stmt = $pdo->prepare('SELECT * FROM police_files WHERE id = ?');
    $stmt->execute([$file_id]);
    $file = $stmt->fetch(PDO::FETCH_ASSOC);
    if (!$file) {
        http_response_code(404);
        echo 'File not found.';
        exit;
    }
    $filepath = __DIR__ . '/../uploads/' . $file['stored_filename'];
    if (!file_exists($filepath)) {
        http_response_code(404);
        echo 'File not found on server.';
        exit;
    }
    // Output headers
    header('Content-Description: File Transfer');
    header('Content-Type: ' . $file['mime_type']);
    header('Content-Disposition: attachment; filename="' . basename($file['original_filename']) . '"');
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    header('Content-Length: ' . filesize($filepath));
    flush();
    readfile($filepath);
    exit;
} catch (Exception $e) {
    http_response_code(500);
    echo 'Server error: ' . htmlspecialchars($e->getMessage());
    exit;
}
