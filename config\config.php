<?php
// config/config.php

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'police_portal');
define('DB_USER', 'root');
define('DB_PASS', 'password'); // <PERSON>AN<PERSON> THIS TO YOUR ACTUAL DATABASE PASSWORD

// Secure Upload Encryption Key
// !!! IMPORTANT: GENERATE YOUR OWN 32-BYTE (256-bit) KEY FOR PRODUCTION !!!
// You can generate a random 32-byte key in PHP using: bin2hex(random_bytes(32))
define('SECURE_UPLOAD_KEY', 'f55f6984bd1088cdaa8f50fd8588e4afe3670d8e84f36ce24571b0b948f1d68b'); // <-- REPLACE THIS!
// Example generated key (DO NOT USE THIS ONE IN PRODUCTION):
// define('SECURE_UPLOAD_KEY', 'a1b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6e7f8a9b0c1d2e3f4a5b6c7d8e9f0a1b2');

// Base directory for uploads (adjust if necessary)
// This should be outside of the web-accessible public directory if possible, for better security
define('UPLOAD_BASE_DIR', dirname(__DIR__) . '/uploads/');