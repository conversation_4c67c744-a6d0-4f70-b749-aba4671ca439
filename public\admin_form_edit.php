<?php
// Admin: Edit Form & Versioning

declare(strict_types=1);
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/_auth_admin.php';

$form_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
if ($form_id < 1) {
    http_response_code(400);
    exit('Invalid form ID');
}

// Fetch form
$stmt = $pdo->prepare('SELECT * FROM FORM WHERE id = ?');
$stmt->execute([$form_id]);
$form = $stmt->fetch();
if (!$form) {
    http_response_code(404);
    exit('Form not found');
}

// Handle update (new version)
$message = null;
$is_error = false;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['formSchema'])) {
    $schema = trim($_POST['formSchema']);
    $json = json_decode($schema, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        $is_error = true;
        $message = 'Invalid JSON schema.';
    } else {
        try {
            $pdo->beginTransaction();
            // Insert new version
            $new_version = $form['version'] + 1;
            $pdo->prepare('INSERT INTO FORM_VERSION (form_id, `schema`, version) VALUES (?, ?, ?)')
                ->execute([$form_id, $schema, $new_version]);
            // Update FORM
            $pdo->prepare('UPDATE FORM SET `schema` = ?, version = ? WHERE id = ?')
                ->execute([$schema, $new_version, $form_id]);
            // Audit log
            $pdo->prepare('INSERT INTO AUDIT_LOG (id, officer_id, action, target, timestamp) VALUES (UUID(), ?, ?, ?, NOW())')
                ->execute([
                    $_SESSION['badge_id'], 'FORM_VERSION_UPDATE', json_encode(['form_id'=>$form_id,'version'=>$new_version])
                ]);
            $pdo->commit();
            $form['schema'] = $schema;
            $form['version'] = $new_version;
            $message = 'Form updated successfully. New version: ' . $new_version;
        } catch (Throwable $e) {
            $pdo->rollBack();
            $is_error = true;
            $message = 'Error updating form: ' . $e->getMessage();
        }
    }
}
// Fetch version history
$versions = $pdo->prepare('SELECT id, version, created_at FROM FORM_VERSION WHERE form_id = ? ORDER BY version DESC');
$versions->execute([$form_id]);
$version_list = $versions->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Form - Police Portal Pro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@3.3.0/dist/tailwind.min.css" rel="stylesheet">
</head>
<body>
<div class="container my-5">
    <h1 class="mb-4">Edit Form: <?= htmlspecialchars($form['name']) ?> <span class="badge bg-warning text-dark">v<?= (int)$form['version'] ?></span></h1>
    <?php if ($message): ?>
        <div class="alert <?= $is_error ? 'alert-danger' : 'alert-success' ?>"> <?= htmlspecialchars($message) ?> </div>
    <?php endif; ?>
    <form method="post" action="" id="visualFormEdit">
        <div class="mb-3">
            <label for="formName" class="form-label">Form Name</label>
            <input type="text" class="form-control" id="formName" name="formName" value="<?= htmlspecialchars($form['name']) ?>" maxlength="100" disabled>
        </div>
        <div class="mb-2"><b>Fields</b></div>
        <div id="fieldsContainer"></div>
        <button type="button" class="btn btn-outline-primary btn-sm mb-3" id="addFieldBtn">+ Add Field</button>
        <input type="hidden" name="formSchema" id="formSchema">
        <div class="mb-3 mt-2">
            <label class="form-label">Live Preview</label>
            <ul class="list-group" id="fieldsPreview"></ul>
        </div>
        <button type="submit" class="btn btn-primary">Save New Version</button>
        <a href="admin_form_builder.php" class="btn btn-secondary ms-2">Back</a>
    </form>
    <script>
    let fields = [];
    // Prepopulate fields from PHP schema
    try {
      fields = JSON.parse(<?= json_encode(json_encode(json_decode($form['schema'], true)['fields'] ?? [])) ?>).map(f => ({
        name: f.name || '',
        type: f.type || 'text',
        required: f.required || false,
        options: f.options || ''
      }));
    } catch(e) { fields = []; }
    function renderFields() {
      const container = document.getElementById('fieldsContainer');
      const preview = document.getElementById('fieldsPreview');
      container.innerHTML = '';
      preview.innerHTML = '';
      fields.forEach((f, i) => {
        const row = document.createElement('div');
        row.className = 'row mb-2 align-items-center';
        row.innerHTML = `
          <div class=\"col-3\"><input type=\"text\" class=\"form-control\" placeholder=\"Field Name\" value=\"${f.name||''}\" onchange=\"fields[${i}].name=this.value;renderFields()\" required></div>
          <div class=\"col-2\">\n            <select class=\"form-select\" onchange=\"fields[${i}].type=this.value;renderFields()\">\n              <option value=\"text\" ${f.type==='text'?'selected':''}>Text</option>\n              <option value=\"number\" ${f.type==='number'?'selected':''}>Number</option>\n              <option value=\"date\" ${f.type==='date'?'selected':''}>Date</option>\n              <option value=\"select\" ${f.type==='select'?'selected':''}>Select</option>\n            </select>\n          </div>\n          <div class=\"col-2\"><input type=\"checkbox\" ${f.required?'checked':''} onchange=\"fields[${i}].required=this.checked;renderFields()\"> Required</div>\n          <div class=\"col-3\">${f.type==='select'?`<input type=\"text\" class=\"form-control\" placeholder=\"Options (comma separated)\" value=\"${f.options||''}\" onchange=\"fields[${i}].options=this.value;renderFields()\">`:''}</div>\n          <div class=\"col-2\">
            <button type=\"button\" class=\"btn btn-secondary btn-sm\" onclick=\"if(${i}>0){[fields[${i}-1],fields[${i}]]=[fields[${i}],fields[${i}-1]];renderFields()}\">↑</button>
            <button type=\"button\" class=\"btn btn-secondary btn-sm\" onclick=\"if(${i}<fields.length-1){[fields[${i}+1],fields[${i}]]=[fields[${i}],fields[${i}+1]];renderFields()}\">↓</button>
            <button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"fields.splice(${i},1);renderFields()\">Remove</button>
          </div>
        `;
        container.appendChild(row);
        const li = document.createElement('li');
        li.className = 'list-group-item';
        let desc = f.name + ' (' + f.type + ')';
        if (f.required) desc += ' [required]';
        if (f.type==='select' && f.options) desc += ' ['+f.options+']';
        li.innerText = desc;
        preview.appendChild(li);
      });
      document.getElementById('formSchema').value = JSON.stringify({fields: fields});
    }
    document.getElementById('addFieldBtn').onclick = function() {
      fields.push({name:'',type:'text',required:false,options:''}); renderFields();
    };
    document.getElementById('visualFormEdit').onsubmit = function(e) {
      renderFields();
    };
    renderFields();
    </script>
    <div class="mt-5">
        <h5>Version History</h5>
        <ul class="list-group">
            <?php foreach ($version_list as $ver): ?>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <form method="get" action="" class="d-inline">
                        <input type="hidden" name="id" value="<?= (int)$form_id ?>">
                        <input type="hidden" name="diff" value="<?= (int)$ver['version'] ?>">
                        <button type="submit" class="btn btn-link p-0">Version <?= (int)$ver['version'] ?></button>
                    </form>
                    <span class="text-muted small">Created: <?= htmlspecialchars($ver['created_at']) ?></span>
                </li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php if (isset($_GET['diff'])):
        $diff_ver = (int)$_GET['diff'];
        $diff_stmt = $pdo->prepare('SELECT schema FROM FORM_VERSION WHERE form_id = ? AND version = ?');
        $diff_stmt->execute([$form_id, $diff_ver]);
        $diff_schema = $diff_stmt->fetchColumn();
        if ($diff_schema):
            $current_schema = json_encode(json_decode($form['schema'], true), JSON_PRETTY_PRINT);
            $old_schema = json_encode(json_decode($diff_schema, true), JSON_PRETTY_PRINT);
    ?>
    <div class="mt-4">
        <h5>Schema Diff: Current vs. Version <?= $diff_ver ?></h5>
        <div class="row">
            <div class="col-md-6">
                <div class="border p-2 bg-light">
                    <strong>Current Schema</strong>
                    <pre style="font-size:12px;white-space:pre-wrap;"><?= htmlspecialchars($current_schema) ?></pre>
                </div>
            </div>
            <div class="col-md-6">
                <div class="border p-2 bg-light">
                    <strong>Version <?= $diff_ver ?></strong>
                    <pre style="font-size:12px;white-space:pre-wrap;"><?= htmlspecialchars($old_schema) ?></pre>
                </div>
            </div>
        </div>
    </div>
    <?php endif; endif; ?>
</div>
</body>
</html>
