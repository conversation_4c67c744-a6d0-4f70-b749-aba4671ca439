<?php
// src/ChatServer.php
namespace App;

use Ratchet\MessageComponentInterface;
use Ratchet\ConnectionInterface;
use PDO;

class ChatServer implements MessageComponentInterface {
    protected \SplObjectStorage $clients;
    protected PDO $pdo;

    public function __construct(PDO $pdo) {
        $this->clients = new \SplObjectStorage;
        $this->pdo = $pdo;
    }

    public function onOpen(ConnectionInterface $conn) {
        $this->clients->attach($conn);
        echo "New connection! ({$conn->resourceId})\n";
        // TODO: Authenticate user via session or token and associate officer_id with $conn
        // Example: $conn->officerId = $_SESSION['officer_id'] ?? null;
        // Broadcast online status to relevant users/channels.
    }

    public function onMessage(ConnectionInterface $from, $msg) {
        $data = json_decode($msg, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            // Handle invalid JSON
            $from->send(json_encode(['error' => 'Invalid JSON']));
            return;
        }

        switch ($data['type'] ?? '') {
            case 'chat_message':
                $this->handleChatMessage($from, $data);
                break;
            case 'online_status_update':
                // TODO: Handle online status updates
                break;
            case 'join_channel':
                // TODO: Handle channel joining for public/private chats
                break;
            // Add cases for other message types as needed
            default:
                echo "Unknown message type received: " . ($data['type'] ?? 'N/A') . "\n";
                break;
        }
    }

    protected function handleChatMessage(ConnectionInterface $from, array $data): void {
        // Basic validation
        // In a real app, officer_id should come from the authenticated connection, not client payload
        $officerId = $from->officerId ?? ($data['officer_id'] ?? null); // For initial testing, might come from payload
        $content = $data['content'] ?? '';
        $channelId = $data['channel_id'] ?? null; // For public/private channels
        $attachments = $data['attachments'] ?? null; // For media, usually this is a URL/identifier

        if (empty($officerId) || empty($content)) {
            $from->send(json_encode(['error' => 'Missing officer ID or message content.']));
            return;
        }

        try {
            // Persist message to database
            $stmt = $this->pdo->prepare("INSERT INTO CHAT_MESSAGE (officer_id, content, timestamp, channel_id, attachments) VALUES (:officer_id, :content, NOW(), :channel_id, :attachments)");
            $stmt->execute([
                ':officer_id' => $officerId,
                ':content' => $content,
                ':channel_id' => $channelId,
                ':attachments' => json_encode($attachments) // Store as JSON
            ]);
            $messageId = $this->pdo->lastInsertId();

            $messageData = [
                'type' => 'chat_message',
                'id' => $messageId,
                'officer_id' => $officerId,
                'content' => $content,
                'timestamp' => date('Y-m-d H:i:s'), // Use a more reliable timestamp if needed
                'channel_id' => $channelId,
                'attachments' => $attachments
            ];

            // Broadcast message to all connected clients (or filter by channel)
            foreach ($this->clients as $client) {
                // TODO: Implement channel-based broadcasting logic
                // if ($client->channelId === $channelId || $channelId === null /* public channel */) {
                $client->send(json_encode($messageData));
                // }
            }
        } catch (\PDOException $e) {
            echo "Database error: " . $e->getMessage() . "\n";
            $from->send(json_encode(['error' => 'Failed to send message: database error.']));
        }
    }

    public function onClose(ConnectionInterface $conn) {
        $this->clients->detach($conn);
        echo "Connection {$conn->resourceId} has disconnected\n";
        // TODO: Update officer's online status to offline in DB and broadcast.
    }

    public function onError(ConnectionInterface $conn, \Exception $e) {
        echo "An error has occurred: {$e->getMessage()}\n";
        $conn->close();
    }
}