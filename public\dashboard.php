<?php
// Police Portal Pro: Dashboard (Admin/User shell)

declare(strict_types=1);
session_set_cookie_params([
    'lifetime' => 900,
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Strict',
]);
session_start();

// Session timeout (15 min inactivity)
if (!isset($_SESSION['badge_id']) || !isset($_SESSION['last_active']) || (time() - $_SESSION['last_active']) > 900) {
    session_unset();
    session_destroy();
    header('Location: /index.php');
    exit;
}
$_SESSION['last_active'] = time();

$badge_id = $_SESSION['badge_id'];
$permissions = $_SESSION['permissions'] ?? [];
$is_admin = in_array('EDIT_PERMISSIONS', $permissions, true);

// Branding
$policeBlue = '#002366';
$policeGold = '#FFD700';
$policeWhite = '#FFFFFF';

?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Police Portal Pro Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@3.3.0/dist/tailwind.min.css" rel="stylesheet">
    <style>
        :root {
            --police-blue: <?= $policeBlue ?>;
            --police-gold: <?= $policeGold ?>;
            --police-white: <?= $policeWhite ?>;
        }
        .header {
            background: linear-gradient(to right, var(--police-blue), #001a4d);
            border-bottom: 3px solid var(--police-gold);
        }
        .badge-icon::before {
            content: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="%23002366" stroke-width="2"><circle cx="12" cy="12" r="10" stroke="%23FFD700" stroke-width="3"/><text x="12" y="16" font-size="12" text-anchor="middle" fill="%23002366">PD</text></svg>');
        }
        body {
            background-color: var(--police-white);
        }
    </style>
</head>
<body>
    <header class="header py-3 d-flex align-items-center">
        <span class="badge-icon me-3"></span>
        <h1 class="mb-0 text-white" style="letter-spacing:2px;">Police Portal Pro</h1>
        <span class="ms-auto text-white">Logged in as <b><?= htmlspecialchars($badge_id) ?></b></span>
    </header>
    <main class="container my-5" style="max-width:900px;">
        <div class="card shadow">
            <div class="card-body">
                <h2 class="card-title mb-4" style="color:var(--police-blue);">Dashboard</h2>
                <?php if ($is_admin): ?>
                <div class="mb-3">
                    <span class="badge bg-warning text-dark">Admin</span>
                    <ul class="list-group mt-3">
                        <li class="list-group-item"><a href="/admin_permission_matrix.php" style="color:var(--police-blue);">Permission Matrix Editor</a></li>
                        <li class="list-group-item"><a href="/admin_form_builder.php" style="color:var(--police-blue);">Form Builder</a></li>
                        <li class="list-group-item"><a href="/admin_form_submissions.php" style="color:var(--police-blue);">Form Submissions</a></li>
                        <li class="list-group-item"><a href="/admin_hierarchy_manager.php" style="color:var(--police-blue);">Hierarchy Manager</a></li>
                        <li class="list-group-item"><a href="/admin_audit_log.php" style="color:var(--police-blue);">Audit Log Explorer</a></li>
                        <li class="list-group-item"><a href="/admin_backup_controller.php" style="color:var(--police-blue);">Backup Controller</a></li>
                        <li class="list-group-item"><a href="/admin_officer_management.php" style="color:var(--police-blue);">Officer Management</a></li>
                    </ul>
                </div>
                <?php else: ?>
                <div class="mb-3">
                    <span class="badge bg-primary">Officer</span>
                    <ul class="list-group mt-3">
                        <li class="list-group-item"><a href="/user_chat.php" style="color:var(--police-blue);">Chat Interface</a></li>
                        <li class="list-group-item"><a href="/user_forms.php" style="color:var(--police-blue);">My Forms</a></li>
                        <li class="list-group-item"><a href="/user_reporting_calendar.php" style="color:var(--police-blue);">Reporting Calendar</a></li>
                        <li class="list-group-item"><a href="/user_file_vault.php" style="color:var(--police-blue);">File Vault</a></li>
                        <li class="list-group-item"><a href="/user_station_map.php" style="color:var(--police-blue);">Station Map</a></li>
                    </ul>
                </div>
                <?php endif; ?>
                <form method="POST" action="/logout.php">
                    <button type="submit" class="btn btn-danger mt-4">Logout</button>
                </form>
            </div>
        </div>
    </main>
</body>
</html>
