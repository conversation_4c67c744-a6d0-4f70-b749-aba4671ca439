<?php
/**
 * Migration Script: Add created_at and rank columns to users table
 * 
 * This script adds the missing columns to the users table and updates
 * existing users with appropriate data from the OFFICER table if available.
 */

declare(strict_types=1);
require_once __DIR__ . '/config/db.php';

echo "=== Adding created_at and rank columns to users table ===\n\n";

try {
    // Step 1: Add created_at column
    echo "Step 1: Adding created_at column...\n";
    try {
        $pdo->exec("ALTER TABLE users ADD COLUMN created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP");
        echo "✅ Added created_at column successfully\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "ℹ️  created_at column already exists\n";
        } else {
            throw $e;
        }
    }
    
    // Step 2: Add rank column
    echo "\nStep 2: Adding rank column...\n";
    try {
        $pdo->exec("ALTER TABLE users ADD COLUMN rank VARCHAR(50) DEFAULT 'Officer'");
        echo "✅ Added rank column successfully\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "ℹ️  rank column already exists\n";
        } else {
            throw $e;
        }
    }
    
    // Step 3: Update existing users with rank data from OFFICER table if available
    echo "\nStep 3: Updating existing users with rank data...\n";
    
    // Get officer data for rank mapping
    $officerRanks = [];
    try {
        $stmt = $pdo->query("SELECT badge_id, rank FROM OFFICER");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $officerRanks[$row['badge_id']] = $row['rank'];
        }
        echo "Found rank data for " . count($officerRanks) . " officers\n";
    } catch (PDOException $e) {
        echo "⚠️  Could not access OFFICER table: " . $e->getMessage() . "\n";
        echo "Will use default ranks for all users\n";
    }
    
    // Update users with ranks
    $stmt = $pdo->prepare("UPDATE users SET rank = ? WHERE username = ?");
    $updatedCount = 0;
    
    foreach ($officerRanks as $username => $rank) {
        try {
            $stmt->execute([$rank, $username]);
            if ($stmt->rowCount() > 0) {
                echo "Updated $username with rank: $rank\n";
                $updatedCount++;
            }
        } catch (PDOException $e) {
            echo "⚠️  Could not update rank for $username: " . $e->getMessage() . "\n";
        }
    }
    
    // Set default ranks for users without specific rank data
    echo "\nStep 4: Setting default ranks for remaining users...\n";
    
    // Set admin rank for admin users
    $stmt = $pdo->prepare("UPDATE users SET rank = 'Administrator' WHERE permission = 'admin' AND (rank IS NULL OR rank = 'Officer')");
    $stmt->execute();
    $adminCount = $stmt->rowCount();
    if ($adminCount > 0) {
        echo "Set 'Administrator' rank for $adminCount admin users\n";
    }
    
    // Set default rank for remaining users
    $stmt = $pdo->prepare("UPDATE users SET rank = 'Officer' WHERE rank IS NULL");
    $stmt->execute();
    $defaultCount = $stmt->rowCount();
    if ($defaultCount > 0) {
        echo "Set default 'Officer' rank for $defaultCount users\n";
    }
    
    // Step 5: Verify the changes
    echo "\nStep 5: Verifying table structure and data...\n";
    
    // Check table structure
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Current users table structure:\n";
    foreach ($columns as $column) {
        echo "  - {$column['Field']}: {$column['Type']} " . 
             ($column['Null'] === 'NO' ? 'NOT NULL' : 'NULL') . 
             ($column['Default'] ? " DEFAULT '{$column['Default']}'" : '') . "\n";
    }
    
    // Show sample user data
    echo "\nSample user data:\n";
    $stmt = $pdo->query("
        SELECT u.username, u.full_name, u.rank, u.permission, u.created_at, o.name as office_name 
        FROM users u 
        JOIN offices o ON u.office_id = o.office_id 
        ORDER BY u.created_at DESC 
        LIMIT 5
    ");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($users as $user) {
        echo "  - {$user['username']}: {$user['full_name']} ({$user['rank']}) - {$user['permission']} at {$user['office_name']}\n";
        echo "    Created: {$user['created_at']}\n";
    }
    
    // Step 6: Update table creation functions for future use
    echo "\nStep 6: Table structure is now updated!\n";
    echo "The users table now includes:\n";
    echo "  ✅ created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP\n";
    echo "  ✅ rank VARCHAR(50) DEFAULT 'Officer'\n";
    
    echo "\n=== Migration completed successfully! ===\n";
    echo "Summary of changes:\n";
    echo "  - Added created_at column with automatic timestamp\n";
    echo "  - Added rank column with default 'Officer'\n";
    echo "  - Updated $updatedCount users with ranks from OFFICER table\n";
    echo "  - Set 'Administrator' rank for $adminCount admin users\n";
    echo "  - Set default 'Officer' rank for $defaultCount remaining users\n";
    
} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
