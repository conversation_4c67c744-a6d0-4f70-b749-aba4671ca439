<?php
/**
 * Migration Script: Update Officer References to User References
 * 
 * This script updates all remaining officer_id references to use user_id
 * and username from the users table instead.
 */

declare(strict_types=1);
require_once __DIR__ . '/config/db.php';

echo "=== Updating Officer References to User References ===\n\n";

try {
    // Step 1: Create mapping of old officer badge_ids to new user_ids
    echo "Step 1: Creating officer to user mapping...\n";
    
    // Load the backup file to get the mapping
    $backupFiles = glob('officer_table_backup_*.json');
    if (empty($backupFiles)) {
        echo "⚠️  No officer backup file found. Will use existing data mapping.\n";
        $officerMapping = [];
    } else {
        $latestBackup = end($backupFiles);
        echo "Using backup file: $latestBackup\n";
        $officerData = json_decode(file_get_contents($latestBackup), true);
        
        // Create mapping from badge_id to username
        $officerMapping = [];
        foreach ($officerData as $officer) {
            $officerMapping[$officer['badge_id']] = $officer['badge_id']; // username is same as badge_id
        }
        echo "Created mapping for " . count($officerMapping) . " officers\n";
    }
    
    // Get current user mapping
    $stmt = $pdo->query("SELECT user_id, username FROM users");
    $userMapping = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $userMapping[$row['username']] = $row['user_id'];
    }
    echo "Found " . count($userMapping) . " users in users table\n";
    
    // Step 2: Update audit_log table
    echo "\nStep 2: Updating audit_log table...\n";
    
    try {
        // Check if audit_log table exists and has officer_id column
        $stmt = $pdo->query("DESCRIBE audit_log");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (in_array('officer_id', $columns)) {
            // Add user_id column if it doesn't exist
            if (!in_array('user_id', $columns)) {
                $pdo->exec("ALTER TABLE audit_log ADD COLUMN user_id INT");
                echo "✅ Added user_id column to audit_log\n";
            }
            
            // Update user_id based on officer_id mapping
            $stmt = $pdo->prepare("UPDATE audit_log SET user_id = ? WHERE officer_id = ?");
            $updatedCount = 0;
            
            foreach ($officerMapping as $badgeId => $username) {
                if (isset($userMapping[$username])) {
                    $stmt->execute([$userMapping[$username], $badgeId]);
                    $updatedCount += $stmt->rowCount();
                }
            }
            
            echo "✅ Updated $updatedCount audit_log records\n";
            
            // Drop officer_id column
            $pdo->exec("ALTER TABLE audit_log DROP COLUMN officer_id");
            echo "✅ Removed officer_id column from audit_log\n";
        } else {
            echo "ℹ️  audit_log table doesn't have officer_id column\n";
        }
    } catch (PDOException $e) {
        echo "⚠️  Could not update audit_log: " . $e->getMessage() . "\n";
    }
    
    // Step 3: Update chat_message table
    echo "\nStep 3: Updating chat_message table...\n";
    
    try {
        $stmt = $pdo->query("DESCRIBE chat_message");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (in_array('officer_id', $columns)) {
            if (!in_array('user_id', $columns)) {
                $pdo->exec("ALTER TABLE chat_message ADD COLUMN user_id INT");
                echo "✅ Added user_id column to chat_message\n";
            }
            
            $stmt = $pdo->prepare("UPDATE chat_message SET user_id = ? WHERE officer_id = ?");
            $updatedCount = 0;
            
            foreach ($officerMapping as $badgeId => $username) {
                if (isset($userMapping[$username])) {
                    $stmt->execute([$userMapping[$username], $badgeId]);
                    $updatedCount += $stmt->rowCount();
                }
            }
            
            echo "✅ Updated $updatedCount chat_message records\n";
            
            $pdo->exec("ALTER TABLE chat_message DROP COLUMN officer_id");
            echo "✅ Removed officer_id column from chat_message\n";
        } else {
            echo "ℹ️  chat_message table doesn't have officer_id column\n";
        }
    } catch (PDOException $e) {
        echo "⚠️  Could not update chat_message: " . $e->getMessage() . "\n";
    }
    
    // Step 4: Update daily_reports table
    echo "\nStep 4: Updating daily_reports table...\n";
    
    try {
        $stmt = $pdo->query("DESCRIBE daily_reports");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $officerColumns = array_filter($columns, function($col) {
            return strpos($col, 'officer_id') !== false;
        });
        
        if (!empty($officerColumns)) {
            // Add user columns
            foreach (['user_id', 'created_by_user', 'edited_by_user'] as $newCol) {
                if (!in_array($newCol, $columns)) {
                    $pdo->exec("ALTER TABLE daily_reports ADD COLUMN $newCol INT");
                    echo "✅ Added $newCol column to daily_reports\n";
                }
            }
            
            // Update the columns
            $mappings = [
                'officer_id' => 'user_id',
                'created_by' => 'created_by_user',
                'edited_by' => 'edited_by_user'
            ];
            
            foreach ($mappings as $oldCol => $newCol) {
                if (in_array($oldCol, $columns)) {
                    $stmt = $pdo->prepare("UPDATE daily_reports SET $newCol = ? WHERE $oldCol = ?");
                    $updatedCount = 0;
                    
                    foreach ($officerMapping as $badgeId => $username) {
                        if (isset($userMapping[$username])) {
                            $stmt->execute([$userMapping[$username], $badgeId]);
                            $updatedCount += $stmt->rowCount();
                        }
                    }
                    
                    echo "✅ Updated $updatedCount daily_reports.$oldCol records\n";
                    
                    $pdo->exec("ALTER TABLE daily_reports DROP COLUMN $oldCol");
                    echo "✅ Removed $oldCol column from daily_reports\n";
                }
            }
        } else {
            echo "ℹ️  daily_reports table doesn't have officer_id columns\n";
        }
    } catch (PDOException $e) {
        echo "⚠️  Could not update daily_reports: " . $e->getMessage() . "\n";
    }
    
    // Step 5: Update form_instance table
    echo "\nStep 5: Updating form_instance table...\n";
    
    try {
        $stmt = $pdo->query("DESCRIBE form_instance");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (in_array('officer_id', $columns)) {
            if (!in_array('user_id', $columns)) {
                $pdo->exec("ALTER TABLE form_instance ADD COLUMN user_id INT");
                echo "✅ Added user_id column to form_instance\n";
            }
            
            $stmt = $pdo->prepare("UPDATE form_instance SET user_id = ? WHERE officer_id = ?");
            $updatedCount = 0;
            
            foreach ($officerMapping as $badgeId => $username) {
                if (isset($userMapping[$username])) {
                    $stmt->execute([$userMapping[$username], $badgeId]);
                    $updatedCount += $stmt->rowCount();
                }
            }
            
            echo "✅ Updated $updatedCount form_instance records\n";
            
            $pdo->exec("ALTER TABLE form_instance DROP COLUMN officer_id");
            echo "✅ Removed officer_id column from form_instance\n";
        } else {
            echo "ℹ️  form_instance table doesn't have officer_id column\n";
        }
    } catch (PDOException $e) {
        echo "⚠️  Could not update form_instance: " . $e->getMessage() . "\n";
    }
    
    // Step 6: Update police_files table
    echo "\nStep 6: Updating police_files table...\n";
    
    try {
        $stmt = $pdo->query("DESCRIBE police_files");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (in_array('officer_badge_id', $columns)) {
            if (!in_array('user_id', $columns)) {
                $pdo->exec("ALTER TABLE police_files ADD COLUMN user_id INT");
                echo "✅ Added user_id column to police_files\n";
            }
            
            $stmt = $pdo->prepare("UPDATE police_files SET user_id = ? WHERE officer_badge_id = ?");
            $updatedCount = 0;
            
            foreach ($officerMapping as $badgeId => $username) {
                if (isset($userMapping[$username])) {
                    $stmt->execute([$userMapping[$username], $badgeId]);
                    $updatedCount += $stmt->rowCount();
                }
            }
            
            echo "✅ Updated $updatedCount police_files records\n";
            
            $pdo->exec("ALTER TABLE police_files DROP COLUMN officer_badge_id");
            echo "✅ Removed officer_badge_id column from police_files\n";
        } else {
            echo "ℹ️  police_files table doesn't have officer_badge_id column\n";
        }
    } catch (PDOException $e) {
        echo "⚠️  Could not update police_files: " . $e->getMessage() . "\n";
    }
    
    // Step 7: Verify all changes
    echo "\nStep 7: Verifying all changes...\n";
    
    $tables = ['audit_log', 'chat_message', 'daily_reports', 'form_instance', 'police_files'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("DESCRIBE $table");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $officerCols = array_filter($columns, function($col) {
                return strpos($col, 'officer') !== false;
            });
            
            $userCols = array_filter($columns, function($col) {
                return strpos($col, 'user') !== false;
            });
            
            echo "Table $table:\n";
            if (!empty($officerCols)) {
                echo "  ⚠️  Still has officer columns: " . implode(', ', $officerCols) . "\n";
            } else {
                echo "  ✅ No officer columns remaining\n";
            }
            
            if (!empty($userCols)) {
                echo "  ✅ Has user columns: " . implode(', ', $userCols) . "\n";
            }
            
        } catch (PDOException $e) {
            echo "  ℹ️  Table $table doesn't exist or is inaccessible\n";
        }
    }
    
    echo "\n=== Officer References Update Completed! ===\n";
    echo "Summary:\n";
    echo "  ✅ Updated all officer_id references to user_id\n";
    echo "  ✅ Removed officer_id columns from all tables\n";
    echo "  ✅ Added user_id columns where needed\n";
    echo "  ✅ Migrated data using officer-to-user mapping\n";
    echo "  ✅ System now fully uses users table for all references\n";
    
} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
