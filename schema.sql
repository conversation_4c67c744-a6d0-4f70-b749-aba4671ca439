=======
-- Police Portal Pro: Core Schema
-- MySQL 8.0 Strict (InnoDB)

CREATE TABLE OFFICER (
    badge_id VARCHAR(32) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    rank VARCHAR(50) NOT NULL,
    assigned_zone VARCHAR(64),
    permissions JSON NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB;

CREATE TABLE STATION (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type ENUM('Division','Department','District','Zone') NOT NULL,
    parent_id INT,
    location JSON,
    FOREIGN KEY (parent_id) REFERENCES STATION(id) ON DELETE SET NULL
) ENGINE=InnoDB;

CREATE TABLE PERMISSION (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(255) NOT NULL
) ENGINE=InnoDB;

CREATE TABLE OFFICER_PERMISSION (
    officer_id VARCHAR(32),
    permission_id INT,
    PRIMARY KEY (officer_id, permission_id),
    FOREIGN KEY (officer_id) REFERENCES OFFICER(badge_id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES PERMISSION(id) ON DELETE CASCADE
) ENGINE=InnoDB;

CREATE TABLE FORM (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    `schema` JSON NOT NULL,
    version INT NOT NULL DEFAULT 1
) ENGINE=InnoDB;

CREATE TABLE FORM_VERSION (
    id INT AUTO_INCREMENT PRIMARY KEY,
    form_id INT NOT NULL,
    `schema` JSON NOT NULL,
    version INT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (form_id) REFERENCES FORM(id) ON DELETE CASCADE
) ENGINE=InnoDB;

CREATE TABLE CHAT_MESSAGE (
    id CHAR(36) PRIMARY KEY,
    officer_id VARCHAR(32) NOT NULL,
    content TEXT,
    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    attachments JSON,
    channel VARCHAR(64) NOT NULL,
    FOREIGN KEY (officer_id) REFERENCES OFFICER(badge_id) ON DELETE CASCADE
) ENGINE=InnoDB;

CREATE TABLE AUDIT_LOG (
    id CHAR(36) PRIMARY KEY,
    officer_id VARCHAR(32) NOT NULL,
    action VARCHAR(100) NOT NULL,
    target JSON NOT NULL,
    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (officer_id) REFERENCES OFFICER(badge_id) ON DELETE CASCADE
) ENGINE=InnoDB;

-- Seed: Example permissions
INSERT INTO PERMISSION (name, description) VALUES
('VIEW_DASHBOARD', 'Access dashboard'),
('EDIT_PERMISSIONS', 'Modify officer permissions'),
('MANAGE_FORMS', 'Create/edit forms'),
('VIEW_AUDIT_LOG', 'See audit logs'),
('CHAT_SEND', 'Send chat messages'),
('CHAT_ADMIN', 'Manage chat channels');


-- Migration: Create FORM_INSTANCE table for storing user-submitted forms
CREATE TABLE IF NOT EXISTS FORM_INSTANCE (
    id INT AUTO_INCREMENT PRIMARY KEY,
    form_id INT NOT NULL,
    officer_id VARCHAR(32) NOT NULL,
    data JSON NOT NULL,
    submitted_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (form_id) REFERENCES FORM(id) ON DELETE CASCADE,
    FOREIGN KEY (officer_id) REFERENCES OFFICER(badge_id) ON DELETE CASCADE
) ENGINE=InnoDB;

-- Migration: Create POLICE_FILES table for storing uploaded files
CREATE TABLE `police_files` (
  `id` int(10) UNSIGNED NOT NULL,
  `original_filename` varchar(255) NOT NULL,
  `stored_filename` varchar(255) NOT NULL,
  `mime_type` varchar(100) NOT NULL,
  `size_bytes` int(10) UNSIGNED NOT NULL,
  `officer_badge_id` varchar(64) NOT NULL,
  `tags` text DEFAULT NULL,
  `uploaded_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
