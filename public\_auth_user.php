<?php
declare(strict_types=1);
session_set_cookie_params([
    'lifetime' => 90000,
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Strict',
]);
session_start();

// Check if user is authenticated (works with both old and new user systems)
if (!isset($_SESSION['badge_id']) || !isset($_SESSION['permissions'])) {
    session_unset();
    session_destroy();
    header('Location: /index.php');
    exit;
}

// Always set officer_badge_id for downstream code (backward compatibility)
$_SESSION['officer_badge_id'] = $_SESSION['badge_id'];

// Set username for new user system compatibility
if (!isset($_SESSION['username'])) {
    $_SESSION['username'] = $_SESSION['badge_id'];
}

// For new users, ensure we have user info available
if (isset($_SESSION['is_new_user']) && $_SESSION['is_new_user']) {
    // User info is already set in login.php for new users
    $username = $_SESSION['username'];
    $userInfo = [
        'full_name' => $_SESSION['full_name'] ?? $username,
        'office_name' => $_SESSION['office_name'] ?? 'Unknown Office',
        'office_level' => $_SESSION['office_level'] ?? '1',
        'access_level' => $_SESSION['permissions'][0] ?? 'user',
        'phone_number' => null // Not available in new system yet
    ];
} else {
    // Legacy user - set basic info
    $username = $_SESSION['badge_id'];
    $userInfo = [
        'full_name' => $username,
        'office_name' => 'Legacy Office',
        'office_level' => '1',
        'access_level' => $_SESSION['permissions'][0] ?? 'user',
        'phone_number' => null
    ];
}
