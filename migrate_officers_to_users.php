<?php
/**
 * Migration Script: Officers to Users Table
 * 
 * This script migrates officers from the OFFICER table to the users table
 * and updates the authentication system to use the users table only.
 */

declare(strict_types=1);
require_once __DIR__ . '/config/db.php';

echo "=== Police Portal Pro: Officer to Users Migration ===\n\n";

try {
    // Step 1: Check current database structure
    echo "Step 1: Checking current database structure...\n";
    
    // Check if OFFICER table exists and get data
    $officerData = [];
    try {
        $stmt = $pdo->query("SELECT badge_id, name, rank, assigned_zone, permissions, password_hash FROM OFFICER");
        $officerData = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "Found " . count($officerData) . " officers in OFFICER table\n";
        foreach ($officerData as $officer) {
            echo "  - {$officer['badge_id']}: {$officer['name']} ({$officer['rank']})\n";
        }
    } catch (PDOException $e) {
        echo "OFFICER table not found or error: " . $e->getMessage() . "\n";
    }
    
    // Step 2: Ensure offices table exists and create Admin Office
    echo "\nStep 2: Setting up offices table...\n";
    
    // Create offices table if it doesn't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS offices (
            office_id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            level ENUM('1', '2', '3', '4') NOT NULL,
            location VARCHAR(255),
            permission VARCHAR(50),
            parent_office_id INT,
            FOREIGN KEY (parent_office_id) REFERENCES offices(office_id) ON DELETE RESTRICT,
            INDEX idx_parent_office_id (parent_office_id),
            INDEX idx_level (level)
        ) ENGINE=InnoDB
    ");
    
    // Check if Admin Office exists, if not create it
    $stmt = $pdo->prepare("SELECT office_id FROM offices WHERE name = 'Admin Office' LIMIT 1");
    $stmt->execute();
    $adminOffice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$adminOffice) {
        $stmt = $pdo->prepare("INSERT INTO offices (name, level, location, permission, parent_office_id) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute(['Admin Office', '4', 'Administrative Headquarters', 'admin', null]);
        $adminOfficeId = $pdo->lastInsertId();
        echo "Created Admin Office with ID: $adminOfficeId\n";
    } else {
        $adminOfficeId = $adminOffice['office_id'];
        echo "Admin Office already exists with ID: $adminOfficeId\n";
    }
    
    // Insert sample offices if none exist (except Admin Office)
    $stmt = $pdo->query("SELECT COUNT(*) FROM offices WHERE name != 'Admin Office'");
    if ($stmt->fetchColumn() == 0) {
        echo "Creating sample office hierarchy...\n";
        $offices = [
            ['Head Office', '4', 'New York HQ', 'admin', null],
            ['Region North', '3', 'Chicago', 'regional', null],
            ['Region South', '3', 'Atlanta', 'regional', null],
            ['District A', '2', 'Chicago Downtown', 'district', null],
            ['District B', '2', 'Chicago Suburbs', 'district', null],
            ['District C', '2', 'Atlanta Metro', 'district', null],
            ['Station A1', '1', 'Loop Area', 'local', null],
            ['Station A2', '1', 'North Side', 'local', null],
            ['Station B1', '1', 'West Side', 'local', null],
            ['Station C1', '1', 'Downtown Atlanta', 'local', null]
        ];
        
        $stmt = $pdo->prepare("INSERT INTO offices (name, level, location, permission, parent_office_id) VALUES (?, ?, ?, ?, ?)");
        foreach ($offices as $office) {
            $stmt->execute($office);
        }
        echo "Created sample office hierarchy\n";
    }
    
    // Step 3: Ensure users table exists with correct structure
    echo "\nStep 3: Setting up users table...\n";
    
    // Create users table with permissions JSON field
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            user_id INT PRIMARY KEY AUTO_INCREMENT,
            full_name VARCHAR(100) NOT NULL,
            username VARCHAR(50) NOT NULL UNIQUE,
            password_hash VARCHAR(255) NOT NULL,
            permission VARCHAR(50),
            permissions JSON,
            office_id INT NOT NULL,
            photo VARCHAR(255) DEFAULT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (office_id) REFERENCES offices(office_id) ON DELETE RESTRICT,
            INDEX idx_username (username),
            INDEX idx_office_id (office_id)
        ) ENGINE=InnoDB
    ");
    
    // Add permissions JSON column if it doesn't exist
    try {
        $pdo->exec("ALTER TABLE users ADD COLUMN permissions JSON");
        echo "Added permissions JSON column to users table\n";
    } catch (PDOException $e) {
        echo "Permissions JSON column already exists or error: " . $e->getMessage() . "\n";
    }
    
    // Step 4: Migrate officers to users table
    echo "\nStep 4: Migrating officers to users table...\n";
    
    if (!empty($officerData)) {
        foreach ($officerData as $officer) {
            // Check if user already exists
            $stmt = $pdo->prepare("SELECT user_id FROM users WHERE username = ? LIMIT 1");
            $stmt->execute([$officer['badge_id']]);
            $existingUser = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($existingUser) {
                echo "User {$officer['badge_id']} already exists, skipping...\n";
                continue;
            }
            
            // Determine permission level and office assignment
            $permissions = json_decode($officer['permissions'], true);
            $permissionLevel = 'user'; // default
            $officeId = 7; // default to Station A1
            
            // Special handling for admin
            if ($officer['badge_id'] === 'A1001') {
                $permissionLevel = 'admin';
                $officeId = $adminOfficeId;
            }
            
            // Insert user
            $stmt = $pdo->prepare("
                INSERT INTO users (full_name, username, password_hash, permission, permissions, office_id)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $officer['name'],
                $officer['badge_id'],
                $officer['password_hash'],
                $permissionLevel,
                $officer['permissions'], // Keep original JSON permissions
                $officeId
            ]);
            
            echo "Migrated {$officer['badge_id']} ({$officer['name']}) as $permissionLevel\n";
        }
    } else {
        echo "No officers found to migrate\n";
    }
    
    // Step 5: Verify migration
    echo "\nStep 5: Verifying migration...\n";
    $stmt = $pdo->query("
        SELECT u.username, u.full_name, u.permission, u.permissions, o.name as office_name 
        FROM users u 
        JOIN offices o ON u.office_id = o.office_id 
        ORDER BY u.username
    ");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Users in database:\n";
    foreach ($users as $user) {
        $perms = $user['permissions'] ? json_decode($user['permissions'], true) : [];
        $permsList = is_array($perms) ? implode(', ', $perms) : 'none';
        echo "  - {$user['username']}: {$user['full_name']} ({$user['permission']}) at {$user['office_name']}\n";
        echo "    Permissions: $permsList\n";
    }
    
    echo "\n=== Migration completed successfully! ===\n";
    echo "Next steps:\n";
    echo "1. Update login.php to use users table only\n";
    echo "2. Update authentication files\n";
    echo "3. Test login with migrated users\n";
    echo "4. Remove OFFICER table when ready\n";
    
} catch (Exception $e) {
    echo "Migration failed: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
