
Police Portal Pro  
Build Objective: Secure internal web portal for police force management with real-time communication, dynamic forms, and granular access control  

---

### Core Functionality  
| Module | Specifications |  
|------------|-------------------|  
| Telegram-Style Chat | Permanent storage • Media sharing (≤25MB) • Public/private channels • Online indicators • No message editing |  
| Station Hierarchy | Drag-drop admin editor • Division→Department→District→Zone • Public location/contact data |  
| File Manager | AI-assisted tagging • SSL encryption • 10-year retention • Hybrid search |  
| Dynamic Form Engine | 51+ CRUD forms • Auto-DB schema • PDF/Excel export • Version control |  
| Permission System | Visual matrix editor • Custom per-officer • Level-based access • Immutable audit logs |  
| Reporting Hub | Time-based submissions • Supervisor approval • Scan uploads |  
| Dashboards | Admin + user views • Role-based customization • Police branding |  

---

### Technical Specifications  
## MANDATORY STACK
FRONTEND: Vanilla JS + Bootstrap 5.3 + Tailwind CSS 3.3  
BACKEND: PHP 8.2 (strict typing)  
SERVER: Apache 2.4 + mod_php  
DATABASE: MySQL 8.0 (InnoDB)  
KEY LIBRARIES:  
  - Ratchet (WebSockets)  
  - TCPDF 6.6  
  - PhpSpreadsheet 1.28  
  - phpSecLib 3.0  

## SECURITY PROTOCOLS
- HTTPS enforcement  
- RBAC on all endpoints  
- Prepared statements only  
- 15-min session timeout  
- Password policy: 12+ chars w/ special chars  

## COMPLIANCE
- All data retained 10 years  
- Daily local ZIP backups (4hr restoration SLA)  
- Immutable audit trails  

---

### Dashboard Implementation  
1. Admin Dashboard  
graph LR
  A[Admin Console] --> B[Permission Matrix Editor]
  A --> C[Form Builder]
  A --> D[Hierarchy Manager]
  A --> E[Audit Log Explorer]
  A --> F[Backup Controller]
  A --> G[Officer Management]

Features:  
- Visual permission grid (officers × 50+ actions)  
- Drag-drop station hierarchy builder  
- Form version diffing tool  
- Real-time backup monitoring  

2. User Dashboard  
graph LR
  U[Officer Workspace] --> UC[Chat Interface]
  U --> UF[My Forms]
  U --> UR[Reporting Calendar]
  U --> UM[File Vault]
  U --> US[Station Map]

Features:  
- Personalized form dashboard (RBAC-filtered)  
- Report due date tracker  
- Quick-access chat channels  
- Tag-based file search  

---

### Police Branding Requirements  
<!-- ENFORCED THEME -->
<style>
  :root {
    --police-blue: #002366;
    --police-gold: #FFD700;
    --police-white: #FFFFFF;
  }
  .header {
    background: linear-gradient(to right, var(--police-blue), #001a4d);
    border-bottom: 3px solid var(--police-gold);
  }
  .badge-icon::before {
    content: url('data:image/svg+xml;utf8,<svg>...</svg>');
  }
</style>

---

### Data Architecture  
erDiagram
    OFFICER {
        string badge_id PK
        string name
        string rank
        string assigned_zone
        json permissions
    }
    STATION {
        int id PK
        string name
        string type "Division|Department|District|Zone"
        int parent_id FK
        json location
    }
    FORM {
        int id PK
        string name
        json schema
        int version
    }
    CHAT_MESSAGE {
        uuid id PK
        int officer_id FK
        text content
        datetime timestamp
        json attachments
    }
    AUDIT_LOG {
        uuid id PK
        int officer_id FK
        string action
        json target
        datetime timestamp
    }

    OFFICER ||--o{ PERMISSION : "custom"
    STATION ||--o{ STATION : "hierarchy"
    FORM ||--o{ FORM_VERSION : "history"
    OFFICER ||--o{ CHAT_MESSAGE : "sends"

---


### AI Agent Build Instructions  
## CONSTRUCTION COMMANDS

### PHASE 1: CORE SYSTEMS
1. BUILD RBAC AUTHENTICATION  
   - PHP session management  
   - Permission matrix database schema  
   - Login with password policy enforcement  

2. IMPLEMENT ADMIN DASHBOARD  
   - Visual permission editor (HTML canvas + JS)  
   - Drag-drop station hierarchy builder  
   - Audit log viewer with filters  

3. SETUP CHAT ENGINE  
   - Ratchet WebSocket server  
   - Message persistence model  
   - Media upload handler (phpSecLib)  

### PHASE 2: USER FUNCTIONALITY
4. CREATE USER DASHBOARD  
   - Role-based component rendering  
   - Telegram-style chat UI  
   - Form quick-access panel  

5. DEVELOP FORM ENGINE  
   - Dynamic DB table generator  
   - PDF/Excel export pipelines  
   - Version control system  

6. BUILD FILE MANAGER  
   - Tag suggestion algorithm (filename analysis)  
   - Encrypted storage directory structure  
   - Hybrid search index  

### PHASE 3: DEPLOYMENT PREP
7. IMPLEMENT BACKUP SYSTEM  
   - Daily cron job (shell script)  
   - ZIP compression  
   - Local storage rotation  

8. APPLY POLICE BRANDING  
   - Enforce color scheme globally  
   - Police badge SVG assets  
   - Responsive testing (320px-4K)  

9. SECURITY HARDENING  
   - OWASP penetration tests  
   - RBAC endpoint validation  
   - Session fixation protection  

### DELIVERABLES
[REQUIRED]
1. Full PHP codebase (PSR-4 structure)  
2. MySQL schema.sql + seed data  
3. Apache .htaccess template  
4. Backup cron script  
5. Test suite (70% coverage)  

[OPTIONAL]  
6. Docker setup for local dev  
7. Load testing script  
8. Dark mode implementation  

---

### Compliance Requirements  
- 🔐 Audit Logs: Record ALL actions with immutable timestamps  
- 💾 Data Retention: 10-year storage for all entities  
- 🔄 Backups: Daily ZIP to /backups (retain 30 days)  
- 🛡 Access Control: Visual permission matrix for admins  

Final Note: All dashboards must render police branding (#002366/#FFD700) and function at 320px viewport width. Chat history persists permanently with basic SSL encryption.