<?php
// Police Portal Pro: Login Handler
// Strict password policy, prepared statements, session fixation protection

declare(strict_types=1);
require_once __DIR__ . '/../vendor/autoload.php';

session_set_cookie_params([
    'lifetime' => 900,
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Strict',
]);
session_start();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    exit('Method Not Allowed');
}

$badge_id = $_POST['badge_id'] ?? '';
$password = $_POST['password'] ?? '';

// Relaxed validation to allow username login from new user system
if (empty($badge_id) || empty($password)) {
    $_SESSION['login_error'] = 'Please enter both username/badge ID and password.';
    header('Location: /index.php');
    exit;
}

require_once __DIR__ . '/../config/db.php';

$user = null;
$isNewUser = false;

// First try the old OFFICER table (for existing users)
try {
    $stmt = $pdo->prepare('SELECT badge_id, password_hash, permissions FROM OFFICER WHERE badge_id = ? LIMIT 1');
    $stmt->execute([$badge_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // OFFICER table might not exist, continue to new users table
}

// If not found in OFFICER table, try the new users table
if (!$user) {
    try {
        $stmt = $pdo->prepare('
            SELECT u.username, u.password_hash, u.permission, u.full_name, u.user_id,
                   o.name as office_name, o.level as office_level
            FROM users u
            JOIN offices o ON u.office_id = o.office_id
            WHERE u.username = ? LIMIT 1
        ');
        $stmt->execute([$badge_id]);
        $newUser = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($newUser) {
            // Convert new user format to old format for compatibility
            $user = [
                'badge_id' => $newUser['username'],
                'password_hash' => $newUser['password_hash'],
                'permissions' => json_encode([$newUser['permission']]), // Convert string to JSON array
                'full_name' => $newUser['full_name'],
                'user_id' => $newUser['user_id'],
                'office_name' => $newUser['office_name'],
                'office_level' => $newUser['office_level']
            ];
            $isNewUser = true;
        }
    } catch (PDOException $e) {
        // users table might not exist
    }
}

if ($user && password_verify($password, $user['password_hash'])) {
    session_regenerate_id(true);
    $_SESSION['badge_id'] = $user['badge_id'];
    $_SESSION['permissions'] = json_decode($user['permissions'], true);
    $_SESSION['last_active'] = time();

    // Store additional info for new users
    if ($isNewUser) {
        $_SESSION['username'] = $user['badge_id'];
        $_SESSION['full_name'] = $user['full_name'];
        $_SESSION['user_id'] = $user['user_id'];
        $_SESSION['office_name'] = $user['office_name'];
        $_SESSION['office_level'] = $user['office_level'];
        $_SESSION['is_new_user'] = true;
    }

    // Audit log (try both tables)
    try {
        $audit = $pdo->prepare('INSERT INTO AUDIT_LOG (id, officer_id, action, target, timestamp) VALUES (UUID(), ?, ?, ?, NOW())');
        $audit->execute([$badge_id, 'LOGIN_SUCCESS', json_encode(['ip'=>$_SERVER['REMOTE_ADDR']])]);
    } catch (PDOException $e) {
        // AUDIT_LOG table might not exist, continue
    }

    header('Location: /dashboard.php');
    exit;
} else {
    // Audit log (try to log failed attempt)
    try {
        $audit = $pdo->prepare('INSERT INTO AUDIT_LOG (id, officer_id, action, target, timestamp) VALUES (UUID(), ?, ?, ?, NOW())');
        $audit->execute([$badge_id, 'LOGIN_FAIL', json_encode(['ip'=>$_SERVER['REMOTE_ADDR']])]);
    } catch (PDOException $e) {
        // AUDIT_LOG table might not exist, continue
    }

    $_SESSION['login_error'] = 'Invalid credentials.';
    header('Location: /index.php');
    exit;
}
