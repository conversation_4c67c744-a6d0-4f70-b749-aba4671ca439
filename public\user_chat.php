<?php
// public/user_chat.php
require dirname(__DIR__) . '/vendor/autoload.php';
require_once dirname(__DIR__) . '/public/_auth_user.php'; // Ensure user is authenticated

// Officer ID from session
$currentOfficerId = $_SESSION['badge_id'] ?? 'DUMMY_OFFICER_ID_123'; // Using badge_id now
$currentOfficerName = $_SESSION['officer_name'] ?? 'Officer'; // Assuming name is stored in session
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Police Chat Portal</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Police Branding - ENFORCED THEME */
        :root {
            --police-blue: #002366;
            --police-gold: #FFD700;
            --police-white: #FFFFFF;
        }

        .header-police {
            background: linear-gradient(to right, var(--police-blue), #001a4d);
            border-bottom: 3px solid var(--police-gold);
            color: var(--police-white);
        }

        /* Police badge icon placeholder */
        .badge-icon::before {
            content: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2L4 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-8-3zm0 2.29l6 2.25V11c0 4.14-2.48 7.97-6 9.38-3.52-1.41-6-5.24-6-9.38V6.54l6-2.25z"/></svg>');
            display: inline-block;
            width: 24px;
            /* Adjust size as needed */
            height: 24px;
            vertical-align: middle;
            margin-right: 5px;
        }

        .chat-container {
            max-width: 900px;
            margin: 20px auto;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            height: 75vh;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .chat-messages {
            flex-grow: 1;
            overflow-y: auto;
            padding: 15px;
            background-color: #fefefe;
            display: flex;
            flex-direction: column;
        }

        .message-bubble {
            padding: 10px 15px;
            border-radius: 20px;
            margin-bottom: 8px;
            max-width: 75%;
            word-wrap: break-word;
            font-size: 0.95rem;
        }

        .message-bubble.other {
            background-color: #e0e0e0;
            color: #333;
            align-self: flex-start;
            border-bottom-left-radius: 5px;
            /* Slightly different corner */
        }

        .message-bubble.self {
            background-color: var(--police-blue);
            color: var(--police-white);
            align-self: flex-end;
            border-bottom-right-radius: 5px;
            /* Slightly different corner */
        }

        .message-info {
            font-size: 0.75rem;
            color: #888;
            margin-top: 2px;
            text-align: right;
        }

        .message-bubble.other .message-info {
            text-align: left;
        }

        .chat-input-area {
            padding: 15px;
            border-top: 1px solid #e0e0e0;
            background-color: #fff;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chat-input {
            flex-grow: 1;
            border-radius: 25px;
            padding: 10px 15px;
            border: 1px solid #ccc;
            font-size: 1rem;
        }

        .btn-send {
            background-color: var(--police-blue);
            color: var(--police-white);
            border: none;
            border-radius: 25px;
            padding: 10px 20px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .btn-send:hover {
            background-color: var(--police-gold);
            color: var(--police-blue);
        }

        .btn-upload {
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            color: #555;
            border-radius: 25px;
            padding: 10px 15px;
            cursor: pointer;
            display: flex;
            align-items: center;
            transition: background-color 0.2s ease;
        }

        .btn-upload:hover {
            background-color: #e0e0e0;
        }

        /* Media attachments in chat */
        .chat-media-attachment img,
        .chat-media-attachment video {
            max-width: 100%;
            border-radius: 8px;
            margin-top: 5px;
        }

        /* Responsiveness for 320px viewport */
        @media (max-width: 768px) {
            .chat-container {
                margin: 10px;
                height: 85vh;
                border-radius: 0;
            }

            .chat-input-area {
                flex-wrap: wrap;
            }

            .chat-input,
            .btn-send,
            .btn-upload {
                width: 100%;
                margin-bottom: 5px;
            }
        }

        @media (max-width: 320px) {
            body {
                font-size: 0.85em;
            }

            .chat-input {
                padding: 8px 10px;
                font-size: 0.9rem;
            }

            .btn-send,
            .btn-upload {
                padding: 8px 15px;
                font-size: 0.9rem;
            }
        }

        /* Styles for channel/user selectors */
        .chat-controls {
            padding: 10px 15px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
            /* Allow wrapping for smaller screens */
        }

        .chat-controls select {
            padding: 8px;
            border-radius: 5px;
            border: 1px solid #ccc;
            font-size: 0.9rem;
            background-color: #fff;
            min-width: 120px;
            /* Ensure dropdowns have a minimum width */
        }
    </style>
</head>

<body class="bg-gray-100 font-sans">
    <header class="header-police p-4 shadow-md flex items-center justify-between">
        <h1 class="text-2xl font-bold flex items-center">
            <span class="badge-icon"></span> Police Chat
        </h1>
        <a href="/dashboard.php" class="text-police-white hover:text-police-gold text-lg">Dashboard</a>
    </header>

    <div class="chat-container shadow-lg">
        <div class="chat-controls">
            <label for="channelSelect" class="font-semibold text-police-blue">Channel:</label>
            <select id="channelSelect" class="flex-grow">
            </select>

            <label for="userSelect" class="font-semibold text-police-blue ml-auto">Officer:</label> <select id="userSelect" class="flex-grow">
                <option value="">All Officers</option>
            </select>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="text-center text-gray-500 p-3" id="loadingHistory">Loading chat history...</div>
        </div>

        <div class="chat-input-area">
            <input type="text" id="messageInput" class="chat-input" placeholder="Type your message...">
            <button id="sendMessageBtn" class="btn-send">Send</button>

            <form id="mediaUploadForm" class="hidden-form" action="/chat_api.php" method="POST" enctype="multipart/form-data">
                <label for="mediaFileInput" class="btn-upload">
                    <svg class="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-4 4 4 4-4V5h-2a1 1 0 110-2h2a3 3 0 013 3v10a3 3 0 01-3 3H4a3 3 0 01-3-3V5a3 3 0 013-3h2a1 1 0 010 2H4zm7-7a1 1 0 11-2 0 1 1 0 012 0zm3-2a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                    </svg>
                    Media
                </label>
                <input type="file" name="chat_media" id="mediaFileInput" class="hidden" accept="image/*,video/*,audio/*,pdf,.doc,.docx,.txt,.zip,.php" multiple>
                <input type="hidden" name="officer_id" value="<?php echo htmlspecialchars($currentOfficerId); ?>">
                <input type="hidden" name="channel_id" id="uploadChannelId" value="">
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Declare officersMap globally so it's accessible everywhere
        let officersMap = {};

        document.addEventListener('DOMContentLoaded', () => {
            const chatMessages = document.getElementById('chatMessages');
            const messageInput = document.getElementById('messageInput');
            const sendMessageBtn = document.getElementById('sendMessageBtn');
            const mediaFileInput = document.getElementById('mediaFileInput');
            const mediaUploadForm = document.getElementById('mediaUploadForm');
            const loadingHistory = document.getElementById('loadingHistory');
            const channelSelect = document.getElementById('channelSelect');
            const userSelect = document.getElementById('userSelect'); // New: User selector

            let currentChannelId = null; // Will store the currently selected channel ID

            // Dynamically inject current officer ID from PHP session
            const currentOfficerId = "<?php echo $currentOfficerId; ?>";
            const currentOfficerName = "<?php echo $currentOfficerName; ?>"; // This can be used for the current user's display name

            // WebSocket connection
            const ws = new WebSocket('ws://localhost:8080'); // Connect to your chat_server.php

            ws.onopen = () => {
                console.log('Connected to WebSocket server');
                // Send initial authentication/identification message for this connection
                ws.send(JSON.stringify({
                    type: 'online_status_update',
                    officer_id: currentOfficerId,
                    status: 'online'
                }));
            };

            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                console.log('Received message:', data);

                switch (data.type) {
                    case 'chat_message':
                        // Only add message if it belongs to the currently selected channel
                        if (data.channel_id == currentChannelId) {
                            // For WebSocket messages, the server might not send 'created_at' or 'officer_name'
                            // The addMessageToChat function will now handle looking up the name.
                            // Ensure 'created_at' is present for new messages (can use current time if not sent by server)
                            if (!data.created_at) {
                                data.created_at = new Date().toISOString(); // Add a timestamp if missing
                            }
                            addMessageToChat(data, data.officer_id === currentOfficerId);
                        }
                        break;
                    case 'online_status_update':
                        // TODO: Update UI with online/offline indicators based on officer_id
                        console.log(`Officer ${data.officer_id} is now ${data.status}`);
                        break;
                        // Handle other message types (e.g., channel updates, system messages)
                }
            };

            ws.onclose = () => {
                console.log('Disconnected from WebSocket server');
                // TODO: Implement re-connection logic
            };

            ws.onerror = (error) => {
                console.error('WebSocket Error:', error);
            };

            // Event listener for sending messages
            sendMessageBtn.addEventListener('click', () => {
                const message = messageInput.value.trim();
                if (message && currentChannelId) { // Ensure a channel is selected
                    const chatMessage = {
                        type: 'chat_message',
                        officer_id: currentOfficerId,
                        content: message,
                        channel_id: currentChannelId, // Use selected channel ID
                        // Add a timestamp to outgoing messages for immediate display and server
                        created_at: new Date().toISOString()
                    };
                    ws.send(JSON.stringify(chatMessage));
                    messageInput.value = ''; // Clear input field
                } else if (!currentChannelId) {
                    alert('Please select a chat channel first.');
                }
            });

            messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    sendMessageBtn.click();
                }
            });

            // Event listener for media file input change
            mediaFileInput.addEventListener('change', () => {
                if (mediaFileInput.files.length > 0 && currentChannelId) { // Ensure a channel is selected
                    const formData = new FormData(mediaUploadForm);
                    formData.set('officer_id', currentOfficerId);
                    formData.set('channel_id', currentChannelId); // Set selected channel ID for the upload

                    fetch(mediaUploadForm.action, {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.status === 'success') {
                                console.log('Media uploaded and message recorded:', data);
                                // The WebSocket server will broadcast the message, so no need to add locally here
                            } else {
                                alert('Media upload failed: ' + data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Error uploading media:', error);
                            alert('An error occurred during media upload.');
                        });
                } else if (!currentChannelId) {
                    alert('Please select a chat channel first to upload media.');
                }
            });


            function addMessageToChat(messageData, isSelf) {
                const messageElement = document.createElement('div');
                messageElement.classList.add('message-bubble', isSelf ? 'self' : 'other');

                let contentHtml = ``;
                if (messageData.content === '[MEDIA_ATTACHMENT]' && messageData.file_path) {
                    const mediaURL = `/chat_api.php?action=get_media&message_id=${messageData.id}`;
                    if (messageData.file_type && messageData.file_type.startsWith('image/')) {
                        contentHtml += `<div class="chat-media-attachment"><img src="${mediaURL}" alt="Image: ${messageData.file_name}" title="${messageData.file_name}"></div>`;
                    } else if (messageData.file_type && messageData.file_type.startsWith('video/')) {
                        contentHtml += `<div class="chat-media-attachment"><video controls src="${mediaURL}" title="${messageData.file_name}"></video></div>`;
                    } else {
                        contentHtml += `<a href="${mediaURL}" target="_blank" class="text-police-gold hover:underline">Download: ${messageData.file_name}</a>`;
                    }
                } else {
                    contentHtml += messageData.content;
                }

                // *** MODIFIED LINE ***
                // Lookup sender name using officersMap for all messages
                // If it's the current user, display "You". Otherwise, try lookup, fallback to badge_id.
                const senderName = isSelf ? 'You' : (officersMap[messageData.officer_id] || messageData.sender_id);
                
                // *** MODIFIED LINE ***
                // Use messageData.created_at for timestamp from DB, or current time for new WebSocket messages
                const timestamp = messageData.created_at ? new Date(messageData.created_at).toLocaleString() : new Date().toLocaleString();


                messageElement.innerHTML = `
                    <div>${contentHtml}</div>
                    <div class="message-info text-xs mt-1">
                        ${senderName} at ${timestamp}
                    </div>
                `;

                chatMessages.appendChild(messageElement);
                chatMessages.scrollTop = chatMessages.scrollHeight; // Auto-scroll to bottom
            }

            // Function to load chat history for the currently selected channel
            function loadChatHistory() {
                if (!currentChannelId) {
                    console.log('No channel selected. Cannot load history.');
                    chatMessages.innerHTML = '<p class="text-center text-gray-500 p-3">Please select a channel to view history.</p>';
                    return;
                }

                chatMessages.innerHTML = ''; // Clear current messages
                loadingHistory.style.display = 'block'; // Show loading indicator

                fetch(`/chat_api.php?action=get_history&channel_id=${currentChannelId}&limit=50`) // Fetch history for selected channel
                    .then(response => response.json())
                    .then(data => {
                        loadingHistory.style.display = 'none'; // Hide loading indicator
                        if (data.status === 'success') {
                            if (data.messages.length === 0) {
                                chatMessages.innerHTML = '<p class="text-center text-gray-500 p-3">No messages in this channel yet.</p>';
                            } else {
                                data.messages.forEach(msg => {
                                    // msg.officer_name is already present from the backend join
                                    addMessageToChat(msg, msg.sender_id === currentOfficerId);
                                });
                            }
                        } else {
                            console.error('Failed to load chat history:', data.message);
                            chatMessages.innerHTML += '<p class="text-center text-red-500">Failed to load chat history: ' + (data.message || 'Unknown error') + '</p>';
                        }
                    })
                    .catch(error => {
                        loadingHistory.style.display = 'none'; // Hide loading indicator
                        console.error('Error fetching chat history:', error);
                        chatMessages.innerHTML += '<p class="text-center text-red-500">Error fetching chat history.</p>';
                    });
            }

            // Function to load available channels into the dropdown
            function loadChannels() {
                fetch('/chat_api.php?action=get_channels')
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success' && data.channels.length > 0) {
                            channelSelect.innerHTML = ''; // Clear existing options
                            data.channels.forEach(channel => {
                                const option = document.createElement('option');
                                option.value = channel.id;
                                option.textContent = channel.name;
                                channelSelect.appendChild(option);
                            });
                            // Automatically select the first channel and load its history
                            if (channelSelect.options.length > 0) {
                                currentChannelId = channelSelect.value;
                                document.getElementById('uploadChannelId').value = currentChannelId; // Update hidden input for upload
                                loadChatHistory();
                            }
                        } else {
                            console.error('Failed to load channels:', data.message || 'No channels found.');
                            channelSelect.innerHTML = '<option value="">No channels available</option>';
                            chatMessages.innerHTML = '<p class="text-center text-red-500 p-3">Could not load chat channels.</p>';
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching channels:', error);
                        channelSelect.innerHTML = '<option value="">Error loading channels</option>';
                        chatMessages.innerHTML = '<p class="text-center text-red-500 p-3">Error fetching chat channels.</p>';
                    });
            }

            // NEW: Function to load available officers into the dropdown AND populate officersMap
            function loadOfficers() {
                fetch('/chat_api.php?action=get_officers')
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success' && data.officers.length > 0) {
                            userSelect.innerHTML = '<option value="">All Officers</option>'; // Keep default
                            data.officers.forEach(officer => {
                                // *** MODIFIED LINE ***
                                // Store officer name in the global map for quick lookup
                                officersMap[officer.badge_id] = officer.name; 

                                const option = document.createElement('option');
                                option.value = officer.badge_id;
                                option.textContent = officer.name + ' (' + officer.badge_id + ')';
                                userSelect.appendChild(option);
                            });
                        } else {
                            console.error('Failed to load officers:', data.message || 'No officers found.');
                            userSelect.innerHTML = '<option value="">Error loading officers</option>';
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching officers:', error);
                        userSelect.innerHTML = '<option value="">Error fetching officers</option>';
                    });
            }


            // Event listener for channel selection change
            channelSelect.addEventListener('change', () => {
                currentChannelId = channelSelect.value;
                document.getElementById('uploadChannelId').value = currentChannelId; // Update hidden input for upload
                loadChatHistory(); // Load history for the newly selected channel
            });

            // Initial loads: Load channels and officers when the page loads
            loadChannels();
            loadOfficers(); // Call the new function to load officers
        });
    </script>
</body>

</html>