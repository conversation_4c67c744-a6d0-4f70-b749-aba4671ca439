<?php
// Secure file serving endpoint for chat/media uploads
// Usage: file.php?path=... (relative to /uploads)

require_once __DIR__ . '/../config/db.php'; // for session/auth
require_once __DIR__ . '/../src/_auth_user.php'; // adjust if needed

$uploadsDir = realpath(__DIR__ . '/../uploads');
if (!$uploadsDir) die('Uploads directory not found.');

if (!isset($_GET['path'])) {
    http_response_code(400);
    echo 'Missing file path.';
    exit;
}

$relPath = str_replace(['..', '\\', "\0"], '', $_GET['path']);
$filePath = realpath($uploadsDir . DIRECTORY_SEPARATOR . $relPath);

// Security: must be inside uploads dir
if (!$filePath || strpos($filePath, $uploadsDir) !== 0 || !is_file($filePath)) {
    http_response_code(404);
    echo 'File not found.';
    exit;
}

// Optionally: check user permissions here (e.g., only channel members)
// ...

// Serve file securely
$mime = mime_content_type($filePath);
header('Content-Type: ' . $mime);
header('Content-Length: ' . filesize($filePath));
header('Content-Disposition: inline; filename="' . basename($filePath) . '"');
readfile($filePath);
exit;
