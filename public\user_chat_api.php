<?php
// API for chat: list messages and send new message (General channel only)
declare(strict_types=1);
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/_auth_user.php';
require_once __DIR__ . '/../utils/SecureUpload.php';
header('Content-Type: application/json');

$action = $_GET['action'] ?? '';
$channel = $_GET['channel'] ?? $_POST['channel'] ?? 'General';
if ($action === 'list') {
    $msgs = $pdo->prepare("SELECT c.id, c.officer_id, o.name AS officer_name, c.content, c.timestamp, c.attachment, c.channel FROM CHAT_MESSAGE c JOIN OFFICER o ON c.officer_id = o.badge_id WHERE c.channel = ? ORDER BY c.timestamp DESC LIMIT 50");
    $msgs->execute([$channel]);
    $msgs = array_reverse($msgs->fetchAll());
    $result = array_map(function($m) {
        return [
            'id' => $m['id'],
            'sender' => $m['officer_id'],
            'sender_name' => $m['officer_name'],
            'content' => nl2br(htmlspecialchars($m['content'])),
            'time' => date('H:i', strtotime($m['timestamp'])),
            'attachment' => $m['attachment'] ? '/file.php?path=chat/' . rawurlencode($m['attachment']) : null,
            'can_delete' => isset($_SESSION['is_admin']) && $_SESSION['is_admin']
        ];
    }, $msgs);
    echo json_encode($result);
    exit;
}
if ($action === 'send' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $content = trim($_POST['content'] ?? '');
    $attachment = null;
    if (!empty($_FILES['chatFile']['name'])) {
        [$ok, $fname] = SecureUpload::handle($_FILES['chatFile'], __DIR__ . '/../uploads/chat/');
        if ($ok) {
            $attachment = $fname;
        } else {
            echo json_encode(['ok'=>0, 'error'=>$fname]);
            exit;
        }
    }
    if (($content !== '' || $attachment) && strlen($content) <= 500) {
        $stmt = $pdo->prepare('INSERT INTO CHAT_MESSAGE (id, channel_id, sender_id, content, file_path, file_name, file_size, file_type, created_at) VALUES (UUID(), ?, ?, ?, ?, ?, ?, ?, NOW())');
        $stmt->execute([
            $channel,
            $_SESSION['badge_id'],
            $content,
            "chat/".$attachment,
            $_FILES['chatFile']['name'] ?? null,
            $_FILES['chatFile']['size'] ?? null,
            $_FILES['chatFile']['type'] ?? null
        ]);
        echo json_encode(['ok'=>1]);
        exit;
    }
    echo json_encode(['ok'=>0, 'error'=>'Empty or too long']);
    exit;
}
if ($action === 'delete' && $_SERVER['REQUEST_METHOD'] === 'POST' && isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
    $id = $_POST['id'] ?? '';
    $pdo->prepare('DELETE FROM CHAT_MESSAGE WHERE id = ?')->execute([$id]);
    echo json_encode(['ok'=>1]);
    exit;
}
if ($action === 'typing' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $_SESSION['chat_typing_'.$channel] = time();
    echo json_encode(['ok'=>1]);
    exit;
}
if ($action === 'typing_status') {
    $is_typing = false;
    foreach ($_SESSION as $k=>$v) {
        if (strpos($k,'chat_typing_'.$channel)===0 && time()-$v < 3) $is_typing = true;
    }
    echo json_encode(['typing'=>$is_typing]);
    exit;
}
echo json_encode(['ok'=>0, 'error'=>'Invalid action']);
