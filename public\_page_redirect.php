<?php
// Police Portal Pro: Page Redirect Handler
// This file handles redirects from old page URLs to the new dashboard-based routing

// Get the current page name
$current_page = basename($_SERVER['PHP_SELF'], '.php');

// Define redirect mappings
$redirect_mappings = [
    'admin_officer_management' => '/dashboard.php?page=admin_officer_management',
    'admin_permission_matrix' => '/dashboard.php?page=admin_permission_matrix',
    'admin_form_builder' => '/dashboard.php?page=admin_form_builder',
    'admin_form_submissions' => '/dashboard.php?page=admin_form_submissions',
    'admin_hierarchy_manager' => '/dashboard.php?page=admin_hierarchy_manager',
    'admin_audit_log' => '/dashboard.php?page=admin_audit_log',
    'admin_backup_controller' => '/dashboard.php?page=admin_backup_controller',
    'user_reporting_calendar' => '/dashboard.php?page=user_reporting_calendar',
    'user_chat' => '/dashboard.php?page=user_chat',
    'user_forms' => '/dashboard.php?page=user_forms',
    'user_file_vault' => '/dashboard.php?page=user_file_vault',
    'user_station_map' => '/dashboard.php?page=user_station_map'
];

// Check if current page should be redirected
if (isset($redirect_mappings[$current_page])) {
    // Preserve any query parameters
    $query_string = $_SERVER['QUERY_STRING'] ?? '';
    $redirect_url = $redirect_mappings[$current_page];
    
    if (!empty($query_string)) {
        $separator = strpos($redirect_url, '?') !== false ? '&' : '?';
        $redirect_url .= $separator . $query_string;
    }
    
    header('Location: ' . $redirect_url);
    exit;
}
?>
