<?php
// Admin: Hierarchy Manager (placeholder)
declare(strict_types=1);
require_once __DIR__ . '/_auth_admin.php';
?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hierarchy Manager - Police Portal Pro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@3.3.0/dist/tailwind.min.css" rel="stylesheet">
</head>
<body>
    <div class="container my-5">
        <h1 class="mb-4">Station Hierarchy Manager <span class="badge bg-warning text-dark">Admin</span></h1>
        <?php
        require_once __DIR__ . '/../config/db.php';
        // Fetch all stations
        $stations = $pdo->query('SELECT id, name, type, parent_id FROM STATION')->fetchAll();
        // Build tree
        function buildTree($elements, $parentId = null) {
            $branch = [];
            foreach ($elements as $element) {
                if ($element['parent_id'] == $parentId) {
                    $children = buildTree($elements, $element['id']);
                    if ($children) {
                        $element['children'] = $children;
                    }
                    $branch[] = $element;
                }
            }
            return $branch;
        }
        $tree = buildTree($stations);
        function renderTree($nodes) {
            if (!$nodes) return;
            echo '<ul class="list-group ms-4">';
            foreach ($nodes as $node) {
                echo '<li class="list-group-item">';
                echo '<b>' . htmlspecialchars($node['name']) . '</b> <span class="badge bg-secondary">' . htmlspecialchars($node['type']) . '</span>';
                if (!empty($node['children'])) {
                    renderTree($node['children']);
                }
                echo '</li>';
            }
            echo '</ul>';
        }
        ?>
        <div class="mb-3">
            <div class="alert alert-info">Drag-and-drop hierarchy editing coming soon.</div>
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Current Station Hierarchy</h5>
                    <?php renderTree($tree); ?>
                </div>
            </div>
        </div>
        <button class="btn btn-primary" disabled>Save Changes (coming soon)</button>
        <a href="/dashboard.php" class="btn btn-secondary ms-2">Back to Dashboard</a>
    </div>
</body>
</html>
