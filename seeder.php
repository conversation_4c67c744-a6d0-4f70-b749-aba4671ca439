<?php
// Police Portal Pro: Simple Seeder Script
// Usage: php seeder.php

declare(strict_types=1);
require_once __DIR__ . '/config/db.php';

function hashpw($pw) {
    return password_hash($pw, PASSWORD_DEFAULT);
}

// Officers
$officers = [
    ['badge_id' => 'A1001', 'name' => 'Alice Admin', 'rank' => 'Chief', 'assigned_zone' => 'Zone 1', 'permissions' => json_encode(['EDIT_PERMISSIONS','MANAGE_FORMS','VIEW_AUDIT_LOG','CHAT_ADMIN','CHAT_SEND']), 'password' => hashpw('Admin$trongP@ss123')],
    ['badge_id' => 'B2002', 'name' => 'Bob Officer', 'rank' => 'Sergeant', 'assigned_zone' => 'Zone 2', 'permissions' => json_encode(['CHAT_SEND']), 'password' => hashpw('Officer$trongP@ss123')],
    ['badge_id' => 'C3003', 'name' => 'Carol User', 'rank' => 'Constable', 'assigned_zone' => 'Zone 3', 'permissions' => json_encode(['CHAT_SEND']), 'password' => hashpw('User$trongP@ss123')],
];

// Stations (Division > Department > District > Zone)
$stations = [
    ['id'=>1, 'name'=>'Central Division', 'type'=>'Division', 'parent_id'=>null, 'location'=>json_encode(['lat'=>40.1,'lng'=>-74.1])],
    ['id'=>2, 'name'=>'Operations Dept', 'type'=>'Department', 'parent_id'=>1, 'location'=>json_encode(['lat'=>40.2,'lng'=>-74.2])],
    ['id'=>3, 'name'=>'North District', 'type'=>'District', 'parent_id'=>2, 'location'=>json_encode(['lat'=>40.3,'lng'=>-74.3])],
    ['id'=>4, 'name'=>'Zone 1', 'type'=>'Zone', 'parent_id'=>3, 'location'=>json_encode(['lat'=>40.4,'lng'=>-74.4])],
    ['id'=>5, 'name'=>'Zone 2', 'type'=>'Zone', 'parent_id'=>3, 'location'=>json_encode(['lat'=>40.5,'lng'=>-74.5])],
];

// Insert officers
foreach ($officers as $o) {
    $stmt = $pdo->prepare('INSERT IGNORE INTO OFFICER (badge_id, name, rank, assigned_zone, permissions, password_hash) VALUES (?, ?, ?, ?, ?, ?)');
    $stmt->execute([$o['badge_id'], $o['name'], $o['rank'], $o['assigned_zone'], $o['permissions'], $o['password']]);
}

// Insert stations
foreach ($stations as $s) {
    $stmt = $pdo->prepare('INSERT IGNORE INTO STATION (id, name, type, parent_id, location) VALUES (?, ?, ?, ?, ?)');
    $stmt->execute([$s['id'], $s['name'], $s['type'], $s['parent_id'], $s['location']]);
}

// Assign permissions (OFFICER_PERMISSION)
$admin_perms = $pdo->query('SELECT id FROM PERMISSION')->fetchAll(PDO::FETCH_COLUMN);
foreach ($officers as $o) {
    if ($o['badge_id'] === 'A1001') {
        foreach ($admin_perms as $pid) {
            $pdo->prepare('INSERT IGNORE INTO OFFICER_PERMISSION (officer_id, permission_id) VALUES (?, ?)')->execute([$o['badge_id'], $pid]);
        }
    } else {
        $pdo->prepare('INSERT IGNORE INTO OFFICER_PERMISSION (officer_id, permission_id) VALUES (?, ?)')->execute([$o['badge_id'], 5]); // CHAT_SEND only
    }
}

// Insert default chat channel (General)
$channelId = sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
    mt_rand(0, 0xffff), mt_rand(0, 0xffff),
    mt_rand(0, 0xffff),
    mt_rand(0, 0x0fff) | 0x4000,
    mt_rand(0, 0x3fff) | 0x8000,
    mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
);
$stmt = $pdo->prepare('INSERT IGNORE INTO CHAT_CHANNEL (id, name, is_private, description, created_by) VALUES (?, ?, 0, ?, ?)');
$stmt->execute([$channelId, 'General', 'General public channel', 'A1001']);

echo "Seed data inserted.\n";
