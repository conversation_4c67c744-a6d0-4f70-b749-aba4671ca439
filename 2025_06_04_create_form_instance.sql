-- Migration: Create FORM_INSTANCE table for storing user-submitted forms
CREATE TABLE IF NOT EXISTS FORM_INSTANCE (
    id INT AUTO_INCREMENT PRIMARY KEY,
    form_id INT NOT NULL,
    officer_id VARCHAR(32) NOT NULL,
    data JSON NOT NULL,
    submitted_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (form_id) REFERENCES FORM(id) ON DELETE CASCADE,
    FOREIGN KEY (officer_id) REFERENCES OFFICER(badge_id) ON DELETE CASCADE
) ENGINE=InnoDB;
