# Police Portal Pro

A secure, real-time management portal for police force operations.

## Stack
- PHP 8.2 (strict)
- MySQL 8.0 (InnoDB)
- Apache 2.4 + mod_php
- Vanilla JS, Bootstrap 5.3, Tailwind CSS 3.3
- Ratchet (WebSockets), TCPDF, PhpSpreadsheet, phpSecLib

## Setup
1. `composer install`
2. Import `schema.sql` into MySQL
3. Configure Apache with `.htaccess`
4. Set up `/backups` directory (writeable)

## Security
- HTTPS enforced
- RBAC on all endpoints
- 15-min session timeout
- 12+ char password policy
- Immutable audit logs

## Branding
Police blue: #002366  |  Police gold: #FFD700
