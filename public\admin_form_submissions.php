<?php
// Admin: View Form Submissions by Form
// Shows a table for each form, listing all submissions for that form

declare(strict_types=1);
require_once __DIR__ . '/../config/db.php';
//require_once __DIR__ . '/_auth_admin.php';
require_once __DIR__ . '/_auth_user.php';

// CSRF token setup
if (session_status() !== PHP_SESSION_ACTIVE) session_start();
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
$csrf_token = $_SESSION['csrf_token'];

// Handle delete
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_id'], $_POST['csrf_token']) && hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
    $del_id = (int)$_POST['delete_id'];
    // Get form_id for audit log
    $stmt = $pdo->prepare('SELECT form_id FROM FORM_INSTANCE WHERE id = ?');
    $stmt->execute([$del_id]);
    $row = $stmt->fetch();
    $pdo->prepare('DELETE FROM FORM_INSTANCE WHERE id = ?')->execute([$del_id]);
    // Audit log
    $pdo->prepare('INSERT INTO AUDIT_LOG (id, officer_id, action, target, timestamp) VALUES (UUID(), ?, ?, ?, NOW())')
        ->execute([
            $_SESSION['badge_id'], 'FORM_INSTANCE_DELETE', json_encode(['submission_id'=>$del_id,'form_id'=>$row['form_id']??null])
        ]);
    header('Location: admin_form_submissions.php');
    exit;
}

$forms = $pdo->query('SELECT id, name, `schema` FROM FORM ORDER BY name')->fetchAll();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Form Submissions - Police Portal Pro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container my-5">
    <h1 class="mb-4">Form Submissions <span class="badge bg-warning text-dark">Admin</span></h1>
    <?php foreach ($forms as $form):
        $fields = json_decode($form['schema'], true)['fields'] ?? [];
        $stmt = $pdo->prepare('SELECT * FROM FORM_INSTANCE WHERE form_id = ? ORDER BY submitted_at DESC');
        $stmt->execute([$form['id']]);
        $instances = $stmt->fetchAll();
    ?>
        <div class="mb-5">
            <h4><?= htmlspecialchars($form['name']) ?></h4>
            <?php if (count($instances) === 0): ?>
                <div class="alert alert-info">No submissions yet.</div>
            <?php else: ?>
                <div class="table-responsive">
                <table class="table table-bordered table-sm">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Officer</th>
                            <?php foreach ($fields as $fld): ?>
                                <th><?= htmlspecialchars($fld['name']) ?></th>
                            <?php endforeach; ?>
                            <th>Submitted At</th>
<th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                    <?php foreach ($instances as $idx => $inst):
                        $data = json_decode($inst['data'], true);
                    ?>
                        <tr>
                            <td><?= $idx+1 ?></td>
                            <td><?= htmlspecialchars($inst['officer_id']) ?></td>
                            <?php foreach ($fields as $fld): ?>
                                <td><?= htmlspecialchars($data[$fld['name']] ?? '') ?></td>
                            <?php endforeach; ?>
                            <td><?= htmlspecialchars($inst['submitted_at']) ?></td>
                            <td>
                                <form method="post" action="" style="display:inline;">
                                    <input type="hidden" name="delete_id" value="<?= (int)$inst['id'] ?>">
                                    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">
                                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Delete this submission?');">Delete</button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                    </tbody>
                </table>
                </div>
            <?php endif; ?>
        </div>
    <?php endforeach; ?>
    <a href="/dashboard.php" class="btn btn-secondary">Back to Dashboard</a>
</div>
</body>
</html>
