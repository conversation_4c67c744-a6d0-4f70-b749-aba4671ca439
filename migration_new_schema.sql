-- Migration: New Office Hierarchy Schema for Reporting System
-- This creates the new schema while preserving existing police portal functionality

-- 1. Create offices table with 4-level hierarchy
CREATE TABLE IF NOT EXISTS offices (
    office_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    level INT NOT NULL CHECK (level IN (1, 2, 3, 4)), -- Restrict to levels 1-4
    location VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20), -- Only additional attribute
    permissions JSON, -- Store office permissions as JSON
    parent_office_id INT, -- Self-referential FK for hierarchy
    FOREIGN KEY (parent_office_id) REFERENCES offices(office_id) ON DELETE RESTRICT,
    UNIQUE (name, level), -- Ensure unique office names per level
    INDEX idx_parent_office (parent_office_id),
    INDEX idx_level (level)
) ENGINE=InnoDB;

-- 2. Create users table
CREATE TABLE IF NOT EXISTS users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    full_name VARCHAR(100) NOT NULL,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL, -- Store hashed passwords
    permissions JSON, -- Store user-specific permissions as JSON
    office_id INT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (office_id) REFERENCES offices(office_id) ON DELETE RESTRICT,
    INDEX idx_office_id (office_id),
    INDEX idx_username (username)
) ENGINE=InnoDB;

-- 3. Create history table for tracking changes
CREATE TABLE IF NOT EXISTS history (
    history_id INT PRIMARY KEY AUTO_INCREMENT,
    entity_type ENUM('office', 'user') NOT NULL,
    entity_id INT NOT NULL, -- References office_id or user_id
    change_type VARCHAR(50) NOT NULL, -- e.g., 'permission_update', 'phone_update'
    old_value JSON, -- Previous value (e.g., old permissions)
    new_value JSON, -- New value (e.g., new permissions)
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    changed_by INT, -- References user_id of who made the change (optional)
    FOREIGN KEY (changed_by) REFERENCES users(user_id) ON DELETE SET NULL,
    INDEX idx_entity (entity_type, entity_id),
    INDEX idx_changed_at (changed_at)
) ENGINE=InnoDB;

-- 4. Update reporting tables to use new schema
CREATE TABLE IF NOT EXISTS office_daily_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_date DATE NOT NULL,
    office_id INT NOT NULL,
    user_id INT NOT NULL,
    report_data JSON NOT NULL,
    version INT NOT NULL DEFAULT 1,
    is_current BOOLEAN NOT NULL DEFAULT TRUE,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by INT NOT NULL,
    edited_by INT NULL,
    edited_at DATETIME NULL,
    FOREIGN KEY (office_id) REFERENCES offices(office_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (edited_by) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_date_office (report_date, office_id),
    INDEX idx_current (is_current),
    UNIQUE KEY unique_current_report (report_date, office_id, is_current, version)
) ENGINE=InnoDB;

-- 5. Office aggregated reports
CREATE TABLE IF NOT EXISTS office_aggregated_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_date DATE NOT NULL,
    office_id INT NOT NULL,
    aggregated_data JSON NOT NULL,
    last_updated DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (office_id) REFERENCES offices(office_id) ON DELETE CASCADE,
    UNIQUE KEY unique_date_office (report_date, office_id),
    INDEX idx_date (report_date)
) ENGINE=InnoDB;

-- 6. Report templates (unchanged but renamed for clarity)
CREATE TABLE IF NOT EXISTS office_report_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    fields JSON NOT NULL,
    office_level INT NOT NULL CHECK (office_level IN (1, 2, 3, 4)),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_level_active (office_level, is_active)
) ENGINE=InnoDB;

-- Insert sample office hierarchy data
INSERT IGNORE INTO offices (office_id, name, level, location, phone_number, permissions, parent_office_id) VALUES
(1, 'Head Office', 4, 'HQ, New York', '555-0100', '{"admin_access": true, "view_all_reports": true, "edit_all_reports": true}', NULL),
(2, 'Region A', 3, 'Chicago', '555-0101', '{"regional_access": true, "view_region_reports": true, "edit_region_reports": true}', 1),
(3, 'Region B', 3, 'Los Angeles', '555-0105', '{"regional_access": true, "view_region_reports": true, "edit_region_reports": true}', 1),
(4, 'Branch A1', 2, 'Chicago North', '555-0102', '{"branch_access": true, "view_branch_reports": true, "edit_branch_reports": true}', 2),
(5, 'Branch A2', 2, 'Chicago South', '555-0106', '{"branch_access": true, "view_branch_reports": true, "edit_branch_reports": true}', 2),
(6, 'Branch B1', 2, 'LA Downtown', '555-0107', '{"branch_access": true, "view_branch_reports": true, "edit_branch_reports": true}', 3),
(7, 'Office A1-1', 1, 'Downtown Chicago', '555-0103', '{"local_access": true, "view_local_reports": true, "edit_local_reports": true}', 4),
(8, 'Office A1-2', 1, 'Uptown Chicago', '555-0104', '{"local_access": true, "view_local_reports": true, "edit_local_reports": true}', 4),
(9, 'Office A2-1', 1, 'South Side Chicago', '555-0108', '{"local_access": true, "view_local_reports": true, "edit_local_reports": true}', 5),
(10, 'Office B1-1', 1, 'LA Central', '555-0109', '{"local_access": true, "view_local_reports": true, "edit_local_reports": true}', 6);

-- Insert sample users
INSERT IGNORE INTO users (user_id, full_name, username, password, permissions, office_id) VALUES
(1, 'John Doe', 'johndoe', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '{"read_write": true, "approve": false}', 7),
(2, 'Jane Smith', 'janesmith', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '{"read_write": true, "approve": true}', 4),
(3, 'Admin User', 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '{"admin_access": true, "approve": true, "view_all": true}', 1),
(4, 'Regional Manager', 'regmanager', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '{"regional_access": true, "approve": true}', 2),
(5, 'Branch Supervisor', 'supervisor', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '{"branch_access": true, "approve": false}', 6);

-- Insert default report template for new schema
INSERT IGNORE INTO office_report_templates (name, description, fields, office_level) VALUES
('Daily Operations Report', 'Standard daily numerical reporting for all offices', 
'{"incidents_reported": {"type": "number", "label": "Incidents Reported", "required": true}, "tasks_completed": {"type": "number", "label": "Tasks Completed", "required": true}, "calls_handled": {"type": "number", "label": "Calls Handled", "required": true}, "work_hours": {"type": "number", "label": "Work Hours", "required": true, "step": "0.1"}, "client_contacts": {"type": "number", "label": "Client Contacts", "required": true}, "documents_processed": {"type": "number", "label": "Documents Processed", "required": true}, "meetings_attended": {"type": "number", "label": "Meetings Attended", "required": true}, "issues_resolved": {"type": "number", "label": "Issues Resolved", "required": true}}', 
1);
