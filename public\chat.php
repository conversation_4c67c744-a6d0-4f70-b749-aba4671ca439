<?php
// Telegram-style Chat UI (PHP+J<PERSON>, Bootstrap+Tailwind)
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../src/_auth_user.php';
?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Police Portal Chat</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@3.3.0/dist/tailwind.min.css" rel="stylesheet">
    <style>
      body { background: #f4f7fa; }
      #chatSidebar { border-right: 1px solid #e5e7eb; }
      #chatArea { height: 80vh; overflow-y: auto; background: #fff; }
      #messageInput { resize: none; }
      .chat-message { margin-bottom: 1.5rem; }
      .chat-message .meta { font-size: 0.85em; color: #888; }
      .chat-message .bubble { background: #f1f1f9; border-radius: 1em; padding: 0.75em 1em; display: inline-block; }
      .chat-message.mine .bubble { background: #dbeafe; }
      .pinned { background: #fffae6; border-left: 4px solid #ffd700; }
    </style>
</head>
<body>
<div class="container-fluid">
  <div class="row">
    <div class="col-md-3 p-0" id="chatSidebar">
      <div class="p-3 border-bottom bg-white">
        <h5 class="mb-2">Channels</h5>
        <ul class="list-group mb-3" id="channelList"></ul>
        <button class="btn btn-primary btn-sm w-100 mb-2" id="createChannelBtn" style="display:none;">+ New Channel</button>
        <div id="userBox" class="mt-4">
          <h6>Online Users</h6>
          <ul class="list-group" id="userList"></ul>
          <div id="dmInfo" class="mt-2 text-primary" style="display:none;"></div>
        </div>
      </div>
    </div>
    <div class="col-md-9 p-0 d-flex flex-column" style="height:100vh;">
      <div class="d-flex align-items-center justify-content-between p-3 border-bottom bg-white">
        <div>
          <span id="currentChannelName" class="fw-bold"></span>
          <span id="channelDesc" class="text-muted ms-2" style="font-size:0.95em;"></span>
        </div>
        <div>
          <button class="btn btn-outline-secondary btn-sm" id="pinBtn">📌 Pinned</button>
          <input type="text" id="searchInput" class="form-control form-control-sm d-inline-block" style="width:180px;" placeholder="Search messages">
        </div>
      </div>
      <div id="pinnedMessages" class="px-3 py-2"></div>
      <div id="chatArea" class="flex-grow-1 px-3 py-2"></div>
      <form id="chatForm" class="d-flex gap-2 p-3 border-top bg-white" enctype="multipart/form-data">
        <textarea id="messageInput" class="form-control" rows="1" maxlength="1000" placeholder="Type a message..."></textarea>
        <input type="file" id="fileInput" class="form-control form-control-sm" style="max-width:180px;" accept="*/*">
        <button class="btn btn-primary" type="submit">Send</button>
      </form>
    </div>
  </div>
</div>
<script>
let officerId = <?php echo json_encode($_SESSION['badge_id']); ?>;
let ws = null, currentChannel = null, channels = [], pinned = [], dmTarget = null, typingTimeout = null;

function loadChannels() {
  fetch('chat_api.php?action=list_channels')
    .then(r=>r.json()).then(res=>{
      if(res.ok) {
        channels = res.channels;
        renderChannelList();
      }
    });
}
function renderChannelList() {
  const ul = document.getElementById('channelList');
  ul.innerHTML = '';
  channels.forEach(c => {
    const li = document.createElement('li');
    li.className = 'list-group-item' + (currentChannel===c.id?' active':'');
    li.textContent = c.name + (c.is_private?' 🔒':'');
    li.onclick = () => joinChannel(c.id, c.name, c.description);
    ul.appendChild(li);
  });
}
function joinChannel(id, name, desc) {
  currentChannel = id;
  dmTarget = null;
  document.getElementById('currentChannelName').textContent = name;
  document.getElementById('channelDesc').textContent = desc||'';
  document.getElementById('dmInfo').style.display = 'none';
  connectWS();
  fetchPinned();
}
function connectWS() {
  if(ws) ws.close();
  ws = new WebSocket('ws://localhost:8080');
  ws.onopen = function() {
    if(dmTarget) {
      ws.send(JSON.stringify({type:'join', officer_id:officerId, dm_officer_id:dmTarget}));
    } else {
      ws.send(JSON.stringify({type:'join', officer_id:officerId, channel_id:currentChannel}));
    }
  };
  ws.onmessage = function(e) {
    const data = JSON.parse(e.data);
    if(data.type==='messages') renderMessages(data.messages);
    if(data.type==='message') addMessage(data.message);
    if(data.type==='online_users') renderUsers(data.users);
    if(data.type==='pinned') renderPinned(data.messages);
    if(data.type==='pin') fetchPinned();
    if(data.type==='search') renderMessages(data.messages);
    if(data.type==='typing') showTyping(data.officer_id);
  };
}

function showTyping(userId) {
  if(!userId || userId === officerId) return;
  let info = document.getElementById('dmInfo');
  if(dmTarget && userId===dmTarget) {
    info.textContent = 'Direct message with ' + dmTarget + ' (typing...)';
    info.style.display = '';
    clearTimeout(typingTimeout);
    typingTimeout = setTimeout(()=>{
      info.textContent = 'Direct message with ' + dmTarget;
    }, 2000);
  } else if(currentChannel) {
    let desc = document.getElementById('channelDesc');
    let orig = desc.textContent.replace(/ \(typing\.\.\.\)$/,'');
    desc.textContent = orig + ' (typing...)';
    clearTimeout(typingTimeout);
    typingTimeout = setTimeout(()=>{
      desc.textContent = orig;
    }, 2000);
  }
}
function renderMessages(msgs) {
  const area = document.getElementById('chatArea');
  area.innerHTML = '';
  msgs.forEach(m => addMessage(m, false));
  area.scrollTop = area.scrollHeight;
}
function addMessage(m, scroll=true) {
  const area = document.getElementById('chatArea');
  const div = document.createElement('div');
  div.className = 'chat-message' + (m.sender_id===officerId?' mine':'');
  let pinnedBadge = m.is_pinned ? '<span class="badge bg-warning ms-2">Pinned</span>' : '';
  let fileLink = '';
  if(m.file) {
    let fname = m.file.name || 'file';
    fileLink = ` <a href='${m.file.url}' target='_blank'>📎 ${fname}</a>`;
  }
  let contentHtml = m.content ? m.content.replace(/\n/g,'<br>') : '';
  let senderDisplay = m.sender_name || m.sender_id;
  div.innerHTML = `<div class='meta'>${senderDisplay} <span class='ms-2'>${m.created_at}</span>${pinnedBadge}</div><div class='bubble${m.is_pinned?' pinned':''}'>${contentHtml}${fileLink}</div>`;
  area.appendChild(div);
  if(scroll) area.scrollTop = area.scrollHeight;
}
function renderUsers(users) {
  const ul = document.getElementById('userList');
  ul.innerHTML = '';
  users.forEach(u => {
    if(u.officer_id === officerId) return; // Don't DM self
    const li = document.createElement('li');
    li.className = 'list-group-item';
    li.textContent = u.officer_id + (u.last_seen?' ('+u.last_seen+')':'');
    li.onclick = () => startDM(u.officer_id);
    ul.appendChild(li);
  });
}

function startDM(targetId) {
  dmTarget = targetId;
  currentChannel = null;
  document.getElementById('currentChannelName').textContent = '[Direct] ' + targetId;
  document.getElementById('channelDesc').textContent = '';
  document.getElementById('dmInfo').style.display = '';
  document.getElementById('dmInfo').textContent = 'Direct message with ' + targetId;
  connectWS();
}
function fetchPinned() {
  if(ws && ws.readyState===1) ws.send(JSON.stringify({type:'join', officer_id:officerId, channel_id:currentChannel}));
}
document.getElementById('chatForm').onsubmit = function(e) {
  e.preventDefault();
  const input = document.getElementById('messageInput');
  const file = document.getElementById('fileInput').files[0];
  const content = input.value.trim();
  if(!content && !file) return;
  if(!ws || ws.readyState!==1) { alert('Chat not connected'); return; }
  if(file) {
    // Upload file via AJAX, then send file info over WS
    const formData = new FormData();
    formData.append('chatFile', file); // must match backend
    formData.append('content', content);
    formData.append('channel', currentChannel);
    fetch('user_chat_api.php?action=send', {method:'POST', body:formData})
      .then(r=>r.json()).then(res=>{
        if(res.ok) {
          // Optionally reload messages or send over WS
          if(dmTarget) {
            ws.send(JSON.stringify({type:'message', officer_id:officerId, dm_officer_id:dmTarget, content, file:res.file}));
          } else {
            ws.send(JSON.stringify({type:'message', officer_id:officerId, channel_id:currentChannel, content, file:res.file}));
          }
        } else alert(res.error||'Upload failed');
      });
  } else {
    if(dmTarget) {
      ws.send(JSON.stringify({type:'message', officer_id:officerId, dm_officer_id:dmTarget, content}));
    } else {
      ws.send(JSON.stringify({type:'message', officer_id:officerId, channel_id:currentChannel, content}));
    }
  }
  input.value = '';
  document.getElementById('fileInput').value = '';
};

document.getElementById('messageInput').oninput = function() {
  if(ws && ws.readyState===1) {
    if(dmTarget) {
      ws.send(JSON.stringify({type:'typing', officer_id:officerId, dm_officer_id:dmTarget}));
    } else if(currentChannel) {
      ws.send(JSON.stringify({type:'typing', officer_id:officerId, channel_id:currentChannel}));
    }
  }
};
document.getElementById('pinBtn').onclick = function() {
  const lastMsg = document.querySelector('#chatArea .chat-message:last-child');
  if(!lastMsg) return;
  const msgId = lastMsg.getAttribute('data-id');
  if(msgId) ws.send(JSON.stringify({type:'pin', channel_id:currentChannel, message_id:msgId}));
};
document.getElementById('searchInput').onchange = function() {
  const q = this.value.trim();
  if(q && ws && ws.readyState===1) ws.send(JSON.stringify({type:'search', channel_id:currentChannel, q}));
};
loadChannels();
</script>
</body>
</html>
