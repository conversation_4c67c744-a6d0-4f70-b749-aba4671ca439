<?php
// Admin: Permission Matrix Editor (placeholder)
declare(strict_types=1);
require_once __DIR__ . '/_auth_admin.php';
?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Permission Matrix Editor - Police Portal Pro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@3.3.0/dist/tailwind.min.css" rel="stylesheet">
</head>
<body>
    <div class="container my-5">
        <h1 class="mb-4">Permission Matrix Editor <span class="badge bg-warning text-dark">Admin</span></h1>
        <?php
        require_once __DIR__ . '/../config/db.php';
        // Fetch officers
        $officers = $pdo->query('SELECT badge_id, name, rank FROM OFFICER ORDER BY name')->fetchAll();
        // Fetch permissions
        $permissions = $pdo->query('SELECT id, name FROM PERMISSION ORDER BY id')->fetchAll();
        ?>
        <?php
        $message = null;
        $is_error = false;
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $pdo->beginTransaction();
                $matrix = $_POST['matrix'] ?? [];
                // Fetch all officers and permissions for reference
                $officer_ids = array_column($officers, 'badge_id');
                $perm_ids = array_column($permissions, 'id');
                // For each officer, update their permissions
                foreach ($officer_ids as $officer_id) {
                    // Remove all current permissions
                    $pdo->prepare('DELETE FROM OFFICER_PERMISSION WHERE officer_id = ?')->execute([$officer_id]);
                    $selected = isset($matrix[$officer_id]) ? array_keys($matrix[$officer_id]) : [];
                    foreach ($selected as $perm_id) {
                        if (in_array($perm_id, $perm_ids)) {
                            $pdo->prepare('INSERT INTO OFFICER_PERMISSION (officer_id, permission_id) VALUES (?, ?)')->execute([$officer_id, $perm_id]);
                        }
                    }
                    // Audit log
                    $pdo->prepare('INSERT INTO AUDIT_LOG (id, officer_id, action, target, timestamp) VALUES (UUID(), ?, ?, ?, NOW())')->execute([
                        $_SESSION['badge_id'], 'PERMISSION_MATRIX_UPDATE', json_encode(['target_officer' => $officer_id, 'permissions' => $selected])
                    ]);
                }
                $pdo->commit();
                $message = 'Permission matrix updated successfully.';
            } catch (Throwable $e) {
                $pdo->rollBack();
                $is_error = true;
                $message = 'Error saving permission matrix: ' . $e->getMessage();
            }
        }
        // Fetch all officer-permission assignments for display
        $assigned = $pdo->query('SELECT officer_id, permission_id FROM OFFICER_PERMISSION')->fetchAll();
        $assigned_map = [];
        foreach ($assigned as $ap) {
            $assigned_map[$ap['officer_id']][$ap['permission_id']] = true;
        }
        ?>
        <?php if ($message): ?>
            <div class="alert <?= $is_error ? 'alert-danger' : 'alert-success' ?>"> <?= htmlspecialchars($message) ?> </div>
        <?php endif; ?>
        <form method="post" action="">
            <div class="table-responsive">
                <table class="table table-bordered align-middle text-center">
                    <thead class="table-light">
                        <tr>
                            <th>Officer</th>
                            <?php foreach ($permissions as $perm): ?>
                                <th style="writing-mode: vertical-lr;"> <?= htmlspecialchars($perm['name']) ?> </th>
                            <?php endforeach; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($officers as $officer): ?>
                        <tr>
                            <td class="text-start">
                                <b><?= htmlspecialchars($officer['name']) ?></b><br>
                                <small><?= htmlspecialchars($officer['badge_id']) ?> | <?= htmlspecialchars($officer['rank']) ?></small>
                            </td>
                            <?php foreach ($permissions as $perm): ?>
                                <td>
                                    <input type="checkbox" name="matrix[<?= htmlspecialchars($officer['badge_id']) ?>][<?= $perm['id'] ?>]" value="1" <?= isset($assigned_map[$officer['badge_id']][$perm['id']]) ? 'checked' : '' ?>>
                                </td>
                            <?php endforeach; ?>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <button type="submit" class="btn btn-primary mt-3">Save Changes</button>
        </form>
        <a href="/dashboard.php" class="btn btn-secondary mt-3">Back to Dashboard</a>
    </div>
</body>
</html>
